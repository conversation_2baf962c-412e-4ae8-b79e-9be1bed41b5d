version: '3'

vars:
  SANDBOX_IMAGE_NAME: salesto:sandbox
  SANDBOX_CONTAINER_PREFIX: salesto-sandbox
  SANDBOX_DOCKERFILE: Dockerfile
  SANDBOX_CONTEXT: .
  SANDBOX_USER: allhands
  SANDBOX_WORKSPACE: /workspace
  HOST_CACHE_DIR: ${HOME}/.cache/salesto-sandbox

tasks:
  build:
    desc: 'Build the sandbox Docker image'
    cmds:
      - |
        echo "Building sandbox image: {{.SANDBOX_IMAGE_NAME}}" >&2
        DOCKER_BUILDKIT=1 docker build \
          --network=host \
          --tag {{.SANDBOX_IMAGE_NAME}} \
          --file {{.SANDBOX_DOCKERFILE}} \
          --target sandbox \
          {{.SANDBOX_CONTEXT}} >&2
        echo "{{.SANDBOX_IMAGE_NAME}}"
    silent: false

  build:force:
    desc: 'Force rebuild the sandbox Docker image (no cache)'
    cmds:
      - |
        echo "Force building sandbox image: {{.SANDBOX_IMAGE_NAME}}" >&2
        DOCKER_BUILDKIT=1 docker build \
          --network=host \
          --no-cache \
          --tag {{.SANDBOX_IMAGE_NAME}} \
          --file {{.SANDBOX_DOCKERFILE}} \
          --target sandbox \
          {{.SANDBOX_CONTEXT}} >&2
        echo "{{.SANDBOX_IMAGE_NAME}}"
    silent: false

  run:
    desc: 'Run a command in the sandbox container (using host Docker socket)'
    cmds:
      - |
        # Create cache directories if they don't exist
        mkdir -p {{.HOST_CACHE_DIR}} {{.HOST_NPM_CACHE}} {{.HOST_YARN_CACHE}} {{.HOST_PIP_CACHE}} {{.HOST_COMPOSER_CACHE}}

        docker run -it --rm \
          --privileged \
          --network=host \
          --security-opt seccomp=unconfined \
          --security-opt apparmor=unconfined \
          --cgroupns=host \
          -v {{.SANDBOX_CONTEXT}}:{{.SANDBOX_WORKSPACE}} \
          -w {{.SANDBOX_WORKSPACE}} \
          -e DOCKER_HOST=unix:///var/run/docker.sock \
          -e SANDBOX_AUTO_SETUP=${SANDBOX_AUTO_SETUP:-true} \
          -v /var/run/docker.sock:/var/run/docker.sock:rw \
          -v /sys/fs/cgroup:/sys/fs/cgroup:rw \
          -v {{.HOST_CACHE_DIR}}:/home/<USER>/.cache/salesto:rw \
          {{.SANDBOX_IMAGE_NAME}} \
          {{.CLI_ARGS}}

  run:dind:
    desc: 'Run a command in the sandbox container (Docker-in-Docker mode)'
    cmds:
      - |
        # Create cache directories if they don't exist
        mkdir -p {{.HOST_CACHE_DIR}} {{.HOST_NPM_CACHE}} {{.HOST_YARN_CACHE}} {{.HOST_PIP_CACHE}} {{.HOST_COMPOSER_CACHE}}

        docker run -it --rm \
          --privileged \
          --network=host \
          --security-opt seccomp=unconfined \
          --security-opt apparmor=unconfined \
          --cgroupns=host \
          -v {{.SANDBOX_CONTEXT}}:{{.SANDBOX_WORKSPACE}} \
          -w {{.SANDBOX_WORKSPACE}} \
          -v /sys/fs/cgroup:/sys/fs/cgroup:rw \
          -v /var/lib/docker \
          -v {{.HOST_CACHE_DIR}}:/home/<USER>/.cache/salesto:rw \
          {{.SANDBOX_IMAGE_NAME}} \
          {{.CLI_ARGS}}

  test:
    desc: 'Run sandbox functionality tests'
    cmds:
      - ./tests/setup/test_sandbox.sh

  test:debug:
    desc: 'Run sandbox functionality tests with debug output'
    cmds:
      - ./tests/setup/test_sandbox.sh --debug

  setup:
    desc: 'Setup sandbox environment (build image and run setup script)'
    cmds:
      - task: build
      - |
        echo "Running setup script in sandbox..."
        task sandbox:run -- ./setup.sh

  setup:dind:
    desc: 'Setup sandbox environment using Docker-in-Docker mode'
    cmds:
      - task: build
      - |
        echo "Running setup script in sandbox (Docker-in-Docker mode)..."
        task sandbox:run:dind -- ./setup.sh

  cache:info:
    desc: 'Show information about cache directories'
    cmds:
      - |
        echo "Cache directories:"
        echo "  General cache: {{.HOST_CACHE_DIR}}"
        echo "  NPM cache: {{.HOST_NPM_CACHE}}"
        echo "  Yarn cache: {{.HOST_YARN_CACHE}}"
        echo "  Pip cache: {{.HOST_PIP_CACHE}}"
        echo "  Composer cache: {{.HOST_COMPOSER_CACHE}}"
        echo ""
        echo "Cache sizes:"
        for dir in "{{.HOST_CACHE_DIR}}" "{{.HOST_NPM_CACHE}}" "{{.HOST_YARN_CACHE}}" "{{.HOST_PIP_CACHE}}" "{{.HOST_COMPOSER_CACHE}}"; do
          if [ -d "$dir" ]; then
            size=$(du -sh "$dir" 2>/dev/null | cut -f1)
            echo "  $dir: $size"
          else
            echo "  $dir: not found"
          fi
        done

  cache:clear:
    desc: 'Clear all sandbox cache directories'
    cmds:
      - |
        echo "Clearing sandbox cache directories..."
        rm -rf {{.HOST_CACHE_DIR}}
        echo "Cleared: {{.HOST_CACHE_DIR}}"

  cache:clear:npm:
    desc: 'Clear NPM cache'
    cmds:
      - |
        echo "Clearing NPM cache..."
        rm -rf {{.HOST_NPM_CACHE}}
        echo "Cleared: {{.HOST_NPM_CACHE}}"

  cache:clear:yarn:
    desc: 'Clear Yarn cache'
    cmds:
      - |
        echo "Clearing Yarn cache..."
        rm -rf {{.HOST_YARN_CACHE}}
        echo "Cleared: {{.HOST_YARN_CACHE}}"

  cache:clear:pip:
    desc: 'Clear Pip cache'
    cmds:
      - |
        echo "Clearing Pip cache..."
        rm -rf {{.HOST_PIP_CACHE}}
        echo "Cleared: {{.HOST_PIP_CACHE}}"

  cache:clear:all:
    desc: 'Clear all package manager caches'
    cmds:
      - task: cache:clear
      - task: cache:clear:npm
      - task: cache:clear:yarn
      - task: cache:clear:pip
