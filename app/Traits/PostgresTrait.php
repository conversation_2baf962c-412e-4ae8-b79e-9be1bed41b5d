<?php

namespace App\Traits;

use Illuminate\Support\Facades\DB;

trait PostgresTrait
{
    /**
     * Create a PostgreSQL schema if it doesn't exist
     *
     * @param string $schemaName
     * @return void
     */
    protected function createSchema(string $schemaName): void
    {
        DB::statement("CREATE SCHEMA IF NOT EXISTS {$schemaName}");
    }

    /**
     * Drop a PostgreSQL schema
     *
     * @param string $schemaName
     * @param bool $cascade
     * @return void
     */
    protected function dropSchema(string $schemaName, bool $cascade = true): void
    {
        $cascadeStatement = $cascade ? 'CASCADE' : '';
        DB::statement("DROP SCHEMA IF EXISTS {$schemaName} {$cascadeStatement}");
    }
}
