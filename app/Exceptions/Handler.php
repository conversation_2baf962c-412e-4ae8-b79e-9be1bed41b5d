<?php

namespace App\Exceptions;

use Filament\Notifications\Notification;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Shared\Domain\OptimisticLockingException;
use Throwable;

class Handler extends ExceptionHandler
{
    /**
     * The list of the inputs that are never flashed to the session on validation exceptions.
     *
     * @var array<int, string>
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     */
    public function register(): void
    {
        $this->reportable(function (Throwable $e) {
            //
        });

        // Handle OptimisticLockingException globally
        $this->renderable(function (OptimisticLockingException $e, $request) {
            if ($request->expectsJson()) {
                return response()->json([
                    'message' => $e->getMessage(),
                ], 409); // HTTP 409 Conflict
            }

            // For Filament admin panel
            if (str_contains($request->url(), '/admin')) {
                Notification::make()
                    ->title('Błąd aktualizacji')
                    ->body($e->getMessage())
                    ->danger()
                    ->send();

                return back()->withInput();
            }

            // For regular web requests
            return back()->withInput()->withErrors([
                'optimistic_locking' => $e->getMessage(),
            ]);
        });
    }
}
