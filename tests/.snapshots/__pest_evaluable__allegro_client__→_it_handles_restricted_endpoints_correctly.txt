a:2:{s:7:"content";s:159:"{"errors":[{"code":"AccessDenied","message":"Access is denied","details":null,"path":null,"userMessage":"No access to the specified resource.","metadata":{}}]}";s:8:"response";O:24:"GuzzleHttp\Psr7\Response":6:{s:38:" GuzzleHttp\Psr7\Response reasonPhrase";s:9:"Forbidden";s:36:" GuzzleHttp\Psr7\Response statusCode";i:403;s:33:" GuzzleHttp\Psr7\Response headers";a:6:{s:8:"trace-id";a:1:{i:0;s:16:"70b17bbb035be461";}s:12:"content-type";a:1:{i:0;s:31:"application/json; charset=utf-8";}s:14:"content-length";a:1:{i:0;s:3:"159";}s:4:"date";a:1:{i:0;s:29:"Mon, 26 May 2025 22:35:20 GMT";}s:25:"strict-transport-security";a:1:{i:0;s:16:"max-age=15552000";}s:22:"x-content-type-options";a:1:{i:0;s:7:"nosniff";}}s:37:" GuzzleHttp\Psr7\Response headerNames";a:6:{s:8:"trace-id";s:8:"trace-id";s:12:"content-type";s:12:"content-type";s:14:"content-length";s:14:"content-length";s:4:"date";s:4:"date";s:25:"strict-transport-security";s:25:"strict-transport-security";s:22:"x-content-type-options";s:22:"x-content-type-options";}s:34:" GuzzleHttp\Psr7\Response protocol";s:3:"1.1";s:32:" GuzzleHttp\Psr7\Response stream";O:22:"GuzzleHttp\Psr7\Stream":7:{s:30:" GuzzleHttp\Psr7\Stream stream";i:0;s:28:" GuzzleHttp\Psr7\Stream size";N;s:32:" GuzzleHttp\Psr7\Stream seekable";b:1;s:32:" GuzzleHttp\Psr7\Stream readable";b:1;s:32:" GuzzleHttp\Psr7\Stream writable";b:1;s:27:" GuzzleHttp\Psr7\Stream uri";s:10:"php://temp";s:38:" GuzzleHttp\Psr7\Stream customMetadata";a:0:{}}}}