<?php

declare(strict_types=1);

namespace Tests\Integration\Order\Domain;

use Order\Domain\Order;
use Shared\Domain\OptimisticLockingException;

test('it can update an order item with version-based locking at the aggregate root level', function () {
    /** @var Order $order */
    $order = Order::factory()->withItem(quantity: 1)->create();

    $item = $order->items->first();
    expect($order->version)->toBe(2);

    $order->updateItem(
        itemId: $item->id,
        name: 'Updated Item',
        price: '150.00',
        quantity: 2,
        sku: 'TEST-SKU-UPDATED',
    );

    expect($order->fresh()->version)->toBe(3);
});

test('it throws an exception when the order version has changed', function () {
    $order = Order::factory()
        ->withItem(
            name: 'Test Item',
            price: '100.00',
            quantity: 1
        )
        ->create();

    expect($order->version)->toBe(2);

    $order->update([
        'notes' => 'Modified by another process',
    ]);

    expect($order->version)->toBe(2);

    $itemId = $order->items->first()->id;
    expect(fn() => $order->updateItem(
        itemId: $itemId,
        name: 'Updated Item',
        price: '150.00',
        quantity: 2,
        sku: 'TEST-SKU-UPDATED',
        version: $order->version - 1
    ))->toThrow(OptimisticLockingException::class);

    expect($order->version)->toBe(2);
    $this->assertDatabaseHas('purchase.order_items', [
        'id' => $itemId,
        'name' => 'Test Item',
        'price' => '100.00',
    ]);
});

test('it can handle sequential updates with version increments', function () {
    $order = Order::factory()->create();

    $item1 = $order->addItem(
        name: 'Item 1',
        price: '100.00',
        quantity: 1,
        sku: 'SKU-1'
    );

    $item2 = $order->addItem(
        name: 'Item 2',
        price: '200.00',
        quantity: 1,
        sku: 'SKU-2'
    );

    $order->updateItem(
        itemId: $item1->id,
        name: 'Updated Item 1',
        price: '150.00',
        quantity: 2,
        sku: 'SKU-1-UPDATED',
        version: 3
    );

    $order->updateItem(
        itemId: $item2->id,
        name: 'Updated Item 2',
        price: '250.00',
        quantity: 3,
        sku: 'SKU-2-UPDATED',
        version: 4
    );

    expect($order->fresh()->version)->toBe(5);
});
