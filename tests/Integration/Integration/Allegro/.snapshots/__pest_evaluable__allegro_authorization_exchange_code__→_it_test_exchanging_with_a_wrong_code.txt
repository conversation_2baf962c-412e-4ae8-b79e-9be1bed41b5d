a:2:{s:7:"content";s:80:"{"error":"invalid_grant","error_description":"Invalid authorization code: code"}";s:8:"response";O:24:"GuzzleHttp\Psr7\Response":6:{s:38:" GuzzleHttp\Psr7\Response reasonPhrase";s:11:"Bad Request";s:36:" GuzzleHttp\Psr7\Response statusCode";i:400;s:33:" GuzzleHttp\Psr7\Response headers";a:15:{s:13:"cache-control";a:1:{i:0;s:8:"no-store";}s:6:"pragma";a:1:{i:0;s:8:"no-cache";}s:4:"date";a:1:{i:0;s:29:"Thu, 17 Apr 2025 15:39:02 GMT";}s:4:"vary";a:1:{i:0;s:84:"Origin,Access-Control-Request-Method,Access-Control-Request-Headers, Accept-Encoding";}s:12:"content-type";a:1:{i:0;s:16:"application/json";}s:3:"age";a:1:{i:0;s:1:"0";}s:23:"content-security-policy";a:1:{i:0;s:71:"block-all-mixed-content; report-uri https://edge.allegro.pl/seclog/csp;";}s:22:"x-content-type-options";a:1:{i:0;s:7:"nosniff";}s:5:"grace";a:1:{i:0;s:4:"none";}s:16:"x-xss-protection";a:1:{i:0;s:21:"1; report=/seclog/xss";}s:17:"transfer-encoding";a:1:{i:0;s:7:"chunked";}s:15:"x-frame-options";a:1:{i:0;s:10:"SAMEORIGIN";}s:25:"strict-transport-security";a:1:{i:0;s:16:"max-age=15552000";}s:10:"set-cookie";a:2:{i:0;s:136:"_cmuid=afc7c0a3-d8f8-4584-81cc-677fe96ac437; Expires=Fri, 22 May 2026 15:39:02 GMT; Path=/; Domain=.allegro.pl.allegrosandbox.pl; Secure";i:1;s:373:"wdctx=v5.WAvB2CJ5IKd74llljbmmtMr6jEKbejTgADTE49zHpKofONekboyjknpmsOdqT0F-H8u6nMXIPI3-gSNaIC2boaosEWMQG3Y5rvJQrDTIKeru7RbW-SKeJfEDq0w5muql0atsRsFSG9HJxeA-V8ODHIdBt-vGP3HG5wgUHOjftqdGgB0XIvKjCVDeiW-bjE0pFAWcpcLa5lpI_0xQ9CTYN0VJ0AuKJoampEtXfUHk_FJkPK3a1yQePs1uy2t6-dAyfTI.sEfGQ3E3QDW7iiNibsPGZw.Bm0_WEhNAV0; Max-Age=432000; Domain=.allegro.pl.allegrosandbox.pl; Path=/; Secure";}s:12:"x-robots-tag";a:1:{i:0;s:7:"noindex";}}s:37:" GuzzleHttp\Psr7\Response headerNames";a:15:{s:13:"cache-control";s:13:"cache-control";s:6:"pragma";s:6:"pragma";s:4:"date";s:4:"date";s:4:"vary";s:4:"vary";s:12:"content-type";s:12:"content-type";s:3:"age";s:3:"age";s:23:"content-security-policy";s:23:"content-security-policy";s:22:"x-content-type-options";s:22:"x-content-type-options";s:5:"grace";s:5:"grace";s:16:"x-xss-protection";s:16:"x-xss-protection";s:17:"transfer-encoding";s:17:"transfer-encoding";s:15:"x-frame-options";s:15:"x-frame-options";s:25:"strict-transport-security";s:25:"strict-transport-security";s:10:"set-cookie";s:10:"set-cookie";s:12:"x-robots-tag";s:12:"x-robots-tag";}s:34:" GuzzleHttp\Psr7\Response protocol";s:3:"1.1";s:32:" GuzzleHttp\Psr7\Response stream";O:22:"GuzzleHttp\Psr7\Stream":7:{s:30:" GuzzleHttp\Psr7\Stream stream";i:0;s:28:" GuzzleHttp\Psr7\Stream size";N;s:32:" GuzzleHttp\Psr7\Stream seekable";b:1;s:32:" GuzzleHttp\Psr7\Stream readable";b:1;s:32:" GuzzleHttp\Psr7\Stream writable";b:1;s:27:" GuzzleHttp\Psr7\Stream uri";s:10:"php://temp";s:38:" GuzzleHttp\Psr7\Stream customMetadata";a:0:{}}}}