<?php

declare(strict_types=1);

/**
 * Combined OAuth server and auth data generator for Allegro API integration tests
 *
 * Run: php -S localhost:8383 server.php
 *
 * This script combines the functionality of server.php and generate_auth_data.php:
 * 1. When accessed at the root URL, it automatically redirects to Allegro's authorization URL
 * 2. When Allegro redirects back with an authorization code, it automatically exchanges it for a token
 * 3. The authorization data is saved to auth_data.json
 */

// Bootstrap Laravel
require '/var/www/html/vendor/autoload.php';
$app = require '/var/www/html/bootstrap/app.php';

// Use the appropriate kernel based on the context
if (php_sapi_name() === 'cli-server') {
    $kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);
    $response = $kernel->handle(
        $request = Illuminate\Http\Request::capture()
    );
} else {
    $kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
    $kernel->bootstrap();
}

use Psr\Log\NullLogger;
use Shared\Integration\Allegro\Adapter\AllegroAuthorization;

// Configuration
$redirectUrl = 'http://localhost:8383';
$authDataFilePath = __DIR__ . '/auth_data.json';

// Get Allegro configuration from Laravel
$allegroConfig = config('services.allegro');

if (empty($allegroConfig)) {
    echo "Error: Allegro configuration not found in config/services.php\n";
    exit(1);
}

// Create AllegroAuthorization instance
$authorization = new AllegroAuthorization(
    $allegroConfig['oauth_token'],
    $allegroConfig['oauth_authorize'],
    $allegroConfig['client_id'],
    $allegroConfig['client_secret'],
    new NullLogger()
);

// Handle OAuth redirect with authorization code
if (isset($_GET['code'])) {
    $code = $_GET['code'];
    $state = $_GET['state'] ?? '';

    try {
        // Exchange authorization code for access token
        $authData = $authorization->exchangeCode($code, $redirectUrl);

        // Save authorization data to file
        file_put_contents($authDataFilePath, json_encode($authData->toArray(), JSON_PRETTY_PRINT));

        // Display success message
        echo "<!DOCTYPE html>
        <html>
        <head>
            <title>Allegro OAuth Test Server</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
                .container { max-width: 800px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
                h1 { color: #333; }
                .success { color: green; }
                pre { background: #f4f4f4; padding: 10px; border-radius: 5px; overflow: auto; white-space: pre-wrap; word-wrap: break-word; }
                .token-info { margin-top: 20px; }
            </style>
        </head>
        <body>
            <div class='container'>
                <h1>Allegro OAuth Test Server</h1>
                <p class='success'>Authorization completed successfully!</p>
                <p>The authorization code has been exchanged for an access token and saved to: <code>{$authDataFilePath}</code></p>
                <div class='token-info'>
                    <p><strong>Token Information:</strong></p>
                    <ul>
                        <li>Access Token: <code>" . substr($authData->accessToken, 0, 10) . "..." . substr($authData->accessToken, -5) . "</code></li>
                        <li>Token Type: <code>{$authData->tokenType}</code></li>
                        <li>Expires In: <code>{$authData->expiredIn} seconds</code></li>
                        <li>Scope: <code>{$authData->scope}</code></li>
                    </ul>
                </div>
                <p>You can now close this window and continue with the tests.</p>
            </div>
        </body>
        </html>";
    } catch (Exception $e) {
        echo "<!DOCTYPE html>
        <html>
        <head>
            <title>Allegro OAuth Test Server</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
                .container { max-width: 800px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
                h1 { color: #333; }
                .error { color: red; }
                pre { background: #f4f4f4; padding: 10px; border-radius: 5px; overflow: auto; white-space: pre-wrap; word-wrap: break-word; }
            </style>
        </head>
        <body>
            <div class='container'>
                <h1>Allegro OAuth Test Server</h1>
                <p class='error'>Error exchanging authorization code:</p>
                <pre>{$e->getMessage()}</pre>
                <p>Please try again by <a href='{$redirectUrl}'>clicking here</a>.</p>
            </div>
        </body>
        </html>";
    }

    posix_kill(getmypid(), SIGINT);
    exit;
}

// Handle saving authorization data (for backward compatibility)
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['auth_data'])) {
    $authData = json_decode($_POST['auth_data'], true);

    // Save authorization data to file
    file_put_contents($authDataFilePath, $_POST['auth_data']);

    echo json_encode(['status' => 'success', 'message' => 'Authorization data has been saved']);
    exit;
}

// Default page - redirect to Allegro authorization URL
$authUrl = $authorization->authorizationUrl($redirectUrl);

// Auto-redirect to Allegro authorization URL
echo "<!DOCTYPE html>
<html>
<head>
    <title>Allegro OAuth Test Server</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
        .container { max-width: 800px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        h1 { color: #333; }
        .redirect-info { margin-top: 20px; color: #666; }
        pre { background: #f4f4f4; padding: 10px; border-radius: 5px; overflow: auto; white-space: pre-wrap; word-wrap: break-word; }
    </style>
    <meta http-equiv='refresh' content='3;url={$authUrl}'>
</head>
<body>
    <div class='container'>
        <h1>Allegro OAuth Test Server</h1>
        <p>Redirecting to Allegro authorization page...</p>
        <p>If you are not redirected automatically, <a href='{$authUrl}'>click here</a>.</p>
        <div class='redirect-info'>
            <p><strong>Authorization URL:</strong></p>
            <pre>{$authUrl}</pre>
        </div>
    </div>
</body>
</html>";
