# Allegro OAuth Test Server

This directory contains tools for testing the OAuth flow for the Allegro API.

## Requirements

- PHP 8.0 or newer
- Internet access
- Allegro Sandbox account
- Registered application in Allegro Sandbox with API permissions

## Usage Instructions

### 1. Start the test server

The test server uses <PERSON><PERSON>'s configuration and environment to handle OAuth redirects and automatically generates the authorization data:

```bash
php -S localhost:8383 server.php
```

The server will listen on port 8383 and:

1. Automatically redirect you to the Allegro authorization page when you access http://localhost:8383
2. Handle the redirect back from Allegro with the authorization code
3. Exchange the code for an access token
4. Save the authorization data to the `auth_data.json` file
5. Display a success message

## Files

- `server.php` - Combined server that handles OAuth redirects, authorization URL generation, and token exchange
- `auth_data.json` - File with authorization data (generated automatically)

## Notes

- Authorization data is valid for a limited time (usually a few hours)
- If tests start returning authorization errors, simply restart the server and go through the authorization process again
- Do not store authorization data in the code repository (the `auth_data.json` file is added to `.gitignore`)

## How it works

1. **OAuth Flow**:

    - The test server is started on port 8383
    - When you access the server in your browser, it automatically redirects to the Allegro authorization URL
    - You log in to Allegro Sandbox and authorize the application
    - Allegro redirects back to the test server with an authorization code
    - The server automatically exchanges the code for an access token
    - The authorization data is saved to a file and a success message is displayed

2. **Integration Tests**:
    - Tests load the authorization data from the file
    - They create an Allegro client with this data
    - They make requests to the Allegro API
    - They verify that the responses are correct

This approach allows for comprehensive testing of the Allegro API client, including the full OAuth flow, with a simplified one-step process.
