<?php

declare(strict_types=1);

use Psr\Log\NullLogger;
use Shared\Integration\Allegro\Adapter\AllegroAuthorization;
use GuzzleHttp\Client;
use GuzzleHttp\HandlerStack;
use Shared\Integration\Abstract\Port\Exception\AllegroAuthorizationException;
use Tests\Stub\SnapshotHttpGuzzleMiddleware;

beforeEach(function () {
    $handlerStack = HandlerStack::create();
    $handlerStack->push(SnapshotHttpGuzzleMiddleware::create(
        __DIR__,
        $this->name()
    ));

    $client = new Client(['handler' => $handlerStack]);

    $this->guzzle = new AllegroAuthorization(
        config('services.allegro.oauth_token'),
        config('services.allegro.oauth_authorize'),
        config('services.allegro.client_id'),
        config('services.allegro.client_secret'),
        new NullLogger(),
        authClient: $client
    );
});

describe('allegro authorization url', function () {
    it('test generate authorize url', function () {
        $result = $this->guzzle->authorizationUrl('http://localhost');

        expect($result)->toMatchSnapshot();
    });

    it('test generate authorize url for empty redirect', function () {
        $result = $this->guzzle->authorizationUrl('');

        expect($result)->toMatchSnapshot();
    });
});

describe('allegro authorization exchange code', function () {
    it('test exchanging with a wrong code', function () {
        $url = 'http://localhost';

        $result = $this->guzzle->exchangeCode('code', $url);

        expect($result)->toMatchSnapshot();
    })->throws(AllegroAuthorizationException::class);

    it('test exchanging code', function () {
        $url = 'http://localhost:81';

        $result = $this->guzzle->exchangeCode('KZVSQL13X3uFl0pEIwll56qpXbw9-sluq1RdoGp1I5mdRTSTaFfci0L4LHKj_99ye5fMG2ZxVrnoWBcRANNKNFDON8zYaqwK0o7I0hdnkXWGNJSCsyujgkw5IoZwcySc', $url);

        expect($result)->toMatchSnapshot();
    });
});
