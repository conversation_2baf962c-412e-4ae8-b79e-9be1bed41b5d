<?php

declare(strict_types=1);

use Shared\Integration\Abstract\Port\Exception\AllegroException;

describe('allegro client', function () {

    it('successfully accesses working endpoints', function (string $endpoint) {
        $client = $this->createAllegroClient();

        $response = $client->get($endpoint);

        expect($response->getStatusCode())->toBe(200);
        expect(json_decode((string)$response->getBody(), true))->toBeArray();
    })->with([
        'categories endpoint' => ['sale/categories'],
        'offers endpoint' => ['sale/offers'],
    ]);

    it('handles restricted endpoints correctly', function (string $endpoint, int $expectedCode, string $expectedMessage) {
        $client = $this->createAllegroClient();

        try {
            $client->get($endpoint);
            $this->fail('Expected exception was not thrown');
        } catch (AllegroException $e) {
            expect($e->getCode())->toBe($expectedCode);
            expect($e->getMessage())->toContain($expectedMessage);
        }
    })->with([
        'me endpoint' => ['me', 403, 'Access is denied'],
        'shipping rates endpoint' => ['sale/shipping-rates', 403, 'Access is denied'],
    ]);

    it('can parse response content', function () {
        $client = $this->createAllegroClient();

        $response = $client->get('sale/categories');
        $content = json_decode($response->getBody()->getContents(), true);

        expect($content)->toBeArray();
        expect($content)->toHaveKey('categories');
    });
});
