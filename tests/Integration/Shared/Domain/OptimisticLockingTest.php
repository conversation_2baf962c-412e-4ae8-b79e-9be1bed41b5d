<?php

declare(strict_types=1);

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Schema;
use Shared\Domain\HasVersionBasedLocking;
use Shared\Domain\OptimisticLockingException;

beforeEach(function () {
    Schema::create('optimistic_locking_test_models', function ($table) {
        $table->uuid('id')->primary();
        $table->string('name');
        $table->integer('version')->default(0);
        $table->timestamps();
        $table->softDeletes();
    });
});

afterEach(function () {
    Schema::dropIfExists('optimistic_locking_test_models');
});

// Define a test model that uses the HasVersionBasedLocking trait
class OptimisticLockingTestModel extends Model
{
    use HasVersionBasedLocking;
    use \Illuminate\Database\Eloquent\Concerns\HasUuids;
    use \Illuminate\Database\Eloquent\SoftDeletes;

    protected $table = 'optimistic_locking_test_models';
    protected $fillable = ['name'];
}

test('it can update a record with version-based locking', function () {
    $model = OptimisticLockingTestModel::create([
        'name' => 'Original Name',
    ]);

    // Initial version should be 1 after creation
    expect($model->getVersion())->toBe(1);

    $result = $model->lock(function() use ($model) {
        $model->name = 'Updated Name';
        $model->save();
        return true;
    });

    expect($result)->toBeTrue();
    expect($model->name)->toBe('Updated Name');
    expect($model->getVersion())->toBe(2);
});

test('it throws an exception when the record has been modified by another process', function () {
    // Create a test record
    $model = OptimisticLockingTestModel::create([
        'name' => 'Original Name',
    ]);

    // Store the original version
    $originalVersion = $model->getVersion();
    expect($originalVersion)->toBe(1);

    // Get a fresh instance to simulate another process
    $anotherProcessModel = OptimisticLockingTestModel::find($model->id);

    // Update via the other process
    $anotherProcessModel->lock(function() use ($anotherProcessModel) {
        $anotherProcessModel->name = 'Modified by another process';
        $anotherProcessModel->save();
        return true;
    });

    // Attempt to update with the original version should throw an exception
    expect(fn () => $model->lock(function() use ($model) {
        $model->name = 'Updated Name';
        $model->save();
        return true;
    }, $originalVersion))->toThrow(OptimisticLockingException::class);

    // Verify the record still has the value set by the "other process"
    $model->refresh();
    expect($model->name)->toBe('Modified by another process');
    expect($model->getVersion())->toBe(2);
});

test('it can update a record without providing a version', function () {
    // Create a test record
    $model = OptimisticLockingTestModel::create([
        'name' => 'Original Name',
    ]);

    // Initial version should be 1
    expect($model->getVersion())->toBe(1);

    // Update without providing a version (should use the current version)
    $result = $model->lock(function() use ($model) {
        $model->name = 'Updated Name';
        $model->save();
        return true;
    });

    // Assert the update was successful
    expect($result)->toBeTrue();

    // Refresh the model from the database
    $model->refresh();

    // Assert the name was updated and version incremented
    expect($model->name)->toBe('Updated Name');
    expect($model->getVersion())->toBe(2);
});

test('it can handle concurrent updates with version-based locking', function () {
    // Create a test record
    $model = OptimisticLockingTestModel::create([
        'name' => 'Original Name',
    ]);

    // Store the original version
    $originalVersion = $model->getVersion();
    expect($originalVersion)->toBe(1);

    // First update succeeds
    $result1 = $model->lock(function() use ($model) {
        $model->name = 'First Update';
        $model->save();
        return true;
    }, $originalVersion);

    expect($result1)->toBeTrue();
    expect($model->getVersion())->toBe(2);

    // Second update with original version fails
    expect(fn () => $model->lock(function() use ($model) {
        $model->name = 'Second Update';
        $model->save();
        return true;
    }, $originalVersion))->toThrow(OptimisticLockingException::class);

    // Refresh and verify the first update persisted
    $model->refresh();
    expect($model->name)->toBe('First Update');
    expect($model->getVersion())->toBe(2);

    // Get the new version and try again
    $newVersion = $model->getVersion();

    // Update with the new version succeeds
    $result3 = $model->lock(function() use ($model) {
        $model->name = 'Third Update';
        $model->save();
        return true;
    }, $newVersion);

    expect($result3)->toBeTrue();

    // Verify the final update
    $model->refresh();
    expect($model->name)->toBe('Third Update');
    expect($model->getVersion())->toBe(3);
});

test('it can use optimisticPush method', function () {
    // Create a test record
    $model = OptimisticLockingTestModel::create([
        'name' => 'Original Name',
    ]);

    // Store the original version
    $originalVersion = $model->getVersion();
    expect($originalVersion)->toBe(1);

    // Update the model
    $model->name = 'Updated with optimisticPush';

    // Use optimisticPush to save changes
    $model->optimisticPush($originalVersion);

    // Refresh the model from the database
    $model->refresh();

    // Assert the name was updated and version incremented
    expect($model->name)->toBe('Updated with optimisticPush');
    expect($model->getVersion())->toBe(2);

    // Attempt to update with the original version should throw an exception
    $model->name = 'This update should fail';
    expect(fn () => $model->optimisticPush($originalVersion))
        ->toThrow(OptimisticLockingException::class);
});
