<?php

declare(strict_types=1);

namespace Tests;

use Shared\Integration\Allegro\Adapter\AllegroClient;
use Shared\Integration\Allegro\Adapter\AllegroClientFactory;
use Shared\Integration\Allegro\Port\AllegroAuthorizationData;

class AllegroTestCase extends TestCase
{
    public function createAllegroClient(): AllegroClient
    {
        $handlerStack = \GuzzleHttp\HandlerStack::create();
        $handlerStack->push(\Tests\Stub\SnapshotHttpGuzzleMiddleware::create(
            __DIR__,
            $this->name()
        ));
        $factory = app(AllegroClientFactory::class, [
            'httpClient' => new \GuzzleHttp\Client([
                'handler' => $handlerStack,
                'base_uri' => config('services.allegro.api_url'),
            ])
        ]);
        $authData = self::getAuthData();

        return $factory->create($authData);
    }

    public function getAuthData(): AllegroAuthorizationData
    {
        $authDataPath = __DIR__ . '/Integration/Integration/Allegro/auth_data.json';
        $arrayAuthData = json_decode(file_get_contents($authDataPath), true);

        return AllegroAuthorizationData::fromArray($arrayAuthData);
    }
}
