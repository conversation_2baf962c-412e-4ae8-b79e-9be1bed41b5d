#!/bin/bash

# Simplified test script for taskfile release functionality
# This script provides a quick test of the basic build and push functionality

set -euo pipefail

# Load test framework
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "${SCRIPT_DIR}/../../BashTestCase.sh"

# Configuration
REGISTRY_PORT=5001
REGISTRY_NAME="simple-test-registry"
REGISTRY_URL="localhost:${REGISTRY_PORT}"
TEST_TAG="simple-test"

# Cleanup function
cleanup_simple_test() {
    print_status "INFO" "Cleaning up..."
    docker_cleanup_container "${REGISTRY_NAME}"
    docker rmi "${REGISTRY_URL}/salesto-php:${TEST_TAG}" 2>/dev/null || true
    docker rmi "${REGISTRY_URL}/salesto-http:${TEST_TAG}" 2>/dev/null || true
}

# Initialize test suite
init_test_suite "Simple Taskfile Release Test" "$TEST_TAG"

# Register cleanup
register_cleanup cleanup_simple_test

# Start registry
print_status "INFO" "Starting local registry..."
docker run -d --name "${REGISTRY_NAME}" -p "${REGISTRY_PORT}:5000" registry:2

# Wait for registry
wait_for_condition \
    "curl -s 'http://${REGISTRY_URL}/v2/'" \
    30 \
    1 \
    "registry"

# Set environment variables for tasks
export REGISTRY_URL="${REGISTRY_URL}"
export IMAGE_TAG="${TEST_TAG}"

# Test dev build and push
call_task_with_status "Dev build and push" "release:build-and-push:dev" "" "CLI_ARGS=${TEST_TAG}"

# Test prod build and push
call_task_with_status "Prod build and push" "release:build-and-push:prod" "" "CLI_ARGS=${TEST_TAG}"

# Verify images in registry
print_status "INFO" "Verifying images in registry..."
sleep 5  # Increased wait time for registry sync

# Debug: Show registry catalog
print_status "INFO" "Registry catalog:"
curl -s "http://${REGISTRY_URL}/v2/_catalog" || print_status "WARN" "Failed to get catalog"

# Check salesto-php image
print_status "INFO" "Checking salesto-php tags..."
PHP_TAGS_RESPONSE=$(curl -s "http://${REGISTRY_URL}/v2/salesto-php/tags/list")
print_status "INFO" "PHP tags response: ${PHP_TAGS_RESPONSE}"

if echo "${PHP_TAGS_RESPONSE}" | grep -q "${TEST_TAG}"; then
    print_status "PASS" "salesto-php image found in registry with tag ${TEST_TAG}"
elif echo "${PHP_TAGS_RESPONSE}" | grep -q "dev\|prod"; then
    print_status "PASS" "salesto-php image found in registry with default tags"
else
    print_status "FAIL" "salesto-php image not found in registry"
    print_status "INFO" "Expected tag: ${TEST_TAG} or default tags (dev/prod)"
fi

# Check salesto-http image
print_status "INFO" "Checking salesto-http tags..."
HTTP_TAGS_RESPONSE=$(curl -s "http://${REGISTRY_URL}/v2/salesto-http/tags/list")
print_status "INFO" "HTTP tags response: ${HTTP_TAGS_RESPONSE}"

if echo "${HTTP_TAGS_RESPONSE}" | grep -q "${TEST_TAG}"; then
    print_status "PASS" "salesto-http image found in registry with tag ${TEST_TAG}"
elif echo "${HTTP_TAGS_RESPONSE}" | grep -q "dev\|prod"; then
    print_status "PASS" "salesto-http image found in registry with default tags"
else
    print_status "FAIL" "salesto-http image not found in registry"
    print_status "INFO" "Expected tag: ${TEST_TAG} or default tags (dev/prod)"
fi

log_success "Registry catalog: http://${REGISTRY_URL}/v2/_catalog"
complete_test_suite 0
