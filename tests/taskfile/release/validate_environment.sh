#!/bin/bash

# Environment validation script for taskfile release tests
# Checks if all prerequisites are met before running the actual tests

# Source the BashTestCase framework
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/../../BashTestCase.sh"

# Initialize test suite
init_test_suite "Taskfile Release Test Environment Validation"
cd "${PROJECT_ROOT}"

print_status "INFO" "Working directory: $(pwd)"

# Check if we're in the right directory
print_status "INFO" "Checking project structure..."

if [ ! -f "taskfile.release.yaml" ]; then
    print_status "FAIL" "taskfile.release.yaml not found. Please run from project root."
    exit 1
else
    print_status "PASS" "taskfile.release.yaml found"
fi

if [ ! -f "Dockerfile" ]; then
    print_status "FAIL" "Dockerfile not found"
    exit 1
else
    print_status "PASS" "Dockerfile found"
fi

if [ ! -f "docker-compose.dev.yml" ]; then
    print_status "FAIL" "docker-compose.dev.yml not found"
    exit 1
else
    print_status "PASS" "docker-compose.dev.yml found"
fi

if [ ! -f "docker-compose.prod.yml" ]; then
    print_status "FAIL" "docker-compose.prod.yml not found"
    exit 1
else
    print_status "PASS" "docker-compose.prod.yml found"
fi

# Check required commands
print_status "INFO" "Checking required commands..."

if ! command -v docker &> /dev/null; then
    print_status "FAIL" "Docker not found. Please install Docker."
    exit 1
else
    print_status "PASS" "Docker found: $(docker --version)"
fi

if ! command -v task &> /dev/null; then
    print_status "FAIL" "Task not found. Please install Task from https://taskfile.dev/"
    exit 1
else
    print_status "PASS" "Task found: $(task --version)"
fi

if ! command -v curl &> /dev/null; then
    print_status "FAIL" "curl not found. Please install curl."
    exit 1
else
    print_status "PASS" "curl found: $(curl --version | head -n1)"
fi

# Check Docker daemon
print_status "INFO" "Checking Docker daemon..."

if docker info &> /dev/null; then
    print_status "PASS" "Docker daemon is running"
else
    print_status "FAIL" "Docker daemon is not running. Please start Docker."
    exit 1
fi

# Check available ports
print_status "INFO" "Checking port availability..."

check_port_available 5000 || print_status "INFO" "Tests may fail if registry can't bind to this port."
check_port_available 5001

# Check taskfile tasks
print_status "INFO" "Checking taskfile tasks..."

check_task_exists "release:build:dev"
check_task_exists "release:build:prod"
check_task_exists "release:build-and-push:dev"
check_task_exists "release:build-and-push:prod"
check_task_exists "release:push:dev"
check_task_exists "release:push:prod"
check_task_exists "release:release"

# Check Dockerfile targets
print_status "INFO" "Checking Dockerfile targets..."

if grep -q "FROM.*AS dev" Dockerfile; then
    print_status "PASS" "dev target found in Dockerfile"
else
    print_status "FAIL" "dev target not found in Dockerfile"
    exit 1
fi

if grep -q "FROM.*AS prod" Dockerfile; then
    print_status "PASS" "prod target found in Dockerfile"
else
    print_status "FAIL" "prod target not found in Dockerfile"
    exit 1
fi

if grep -q "FROM.*AS http-base" Dockerfile; then
    print_status "PASS" "http-base target found in Dockerfile"
else
    print_status "FAIL" "http-base target not found in Dockerfile"
    exit 1
fi

if grep -q "FROM.*AS http-prod" Dockerfile; then
    print_status "PASS" "http-prod target found in Dockerfile"
else
    print_status "FAIL" "http-prod target not found in Dockerfile"
    exit 1
fi

# Check test scripts
print_status "INFO" "Checking test scripts..."

if [ -f "tests/taskfile/release/test_release.sh" ] && [ -x "tests/taskfile/release/test_release.sh" ]; then
    print_status "PASS" "test_release.sh found and executable"
else
    print_status "FAIL" "test_release.sh not found or not executable"
    complete_test_suite 1
fi

if [ -f "tests/taskfile/release/test_release_simple.sh" ] && [ -x "tests/taskfile/release/test_release_simple.sh" ]; then
    print_status "PASS" "test_release_simple.sh found and executable"
else
    print_status "FAIL" "test_release_simple.sh not found or not executable"
    complete_test_suite 1
fi

log_success "Environment Validation Completed Successfully!"
log_info "You can now run the taskfile release tests:"
log_info "  ./tests/taskfile/release/test_release.sh        # Comprehensive test suite"
log_info "  ./tests/taskfile/release/test_release_simple.sh # Quick test"

complete_test_suite 0