#!/bin/bash

# Test script for taskfile release functionality
# This script tests building and pushing dev and prod images to a local registry using Docker-in-Docker

set -euo pipefail

# Load test framework
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "${SCRIPT_DIR}/../../BashTestCase.sh"

# Configuration
REGISTRY_PORT=5002
REGISTRY_NAME="test-registry"
REGISTRY_URL="localhost:${REGISTRY_PORT}"
TEST_TAG="$(generate_test_id)"
DOCKER_NETWORK="test-network"

# Cleanup function
cleanup_release_test() {
    print_status "INFO" "Cleaning up test environment..."

    # Stop and remove registry container
    docker_cleanup_container "${REGISTRY_NAME}"

    # Remove test network
    docker network rm "${DOCKER_NETWORK}" 2>/dev/null || true

    # Clean up test images
    docker rmi "${REGISTRY_URL}/salesto-php:${TEST_TAG}" 2>/dev/null || true
    docker rmi "${REGISTRY_URL}/salesto-http:${TEST_TAG}" 2>/dev/null || true

    print_status "INFO" "Cleanup completed"
}

# Function to wait for registry to be ready
wait_for_registry() {
    wait_for_condition \
        "curl -s 'http://${REGISTRY_URL}/v2/'" \
        30 \
        1 \
        "registry"
}

# Note: check_image_in_registry function is now provided by BashTestCase framework

# Function to test image build
test_build() {
    local env=$1
    print_status "INFO" "Testing build:${env} with tag ${TEST_TAG}"

    # Set environment variables for the build
    export REGISTRY_URL="${REGISTRY_URL}"
    export IMAGE_TAG="${TEST_TAG}"

    # Run the build task
    if call_task "release:build:${env}" "" "CLI_ARGS=${TEST_TAG}"; then
        print_status "PASS" "release:build:${env} completed successfully"

        # Check if images were built locally
        if docker images | grep -q "${REGISTRY_URL}/salesto-php.*${TEST_TAG}"; then
            print_status "PASS" "salesto-php:${TEST_TAG} image built successfully"
        else
            print_status "FAIL" "salesto-php:${TEST_TAG} image not found locally"
        fi

        if docker images | grep -q "${REGISTRY_URL}/salesto-http.*${TEST_TAG}"; then
            print_status "PASS" "salesto-http:${TEST_TAG} image built successfully"
        else
            print_status "FAIL" "salesto-http:${TEST_TAG} image not found locally"
        fi
    else
        print_status "FAIL" "build:${env} failed"
    fi
}

# Function to test image push
test_push() {
    local env=$1
    print_status "INFO" "Testing push:${env} with tag ${TEST_TAG}"

    # Set environment variables for the push
    export REGISTRY_URL="${REGISTRY_URL}"
    export IMAGE_TAG="${TEST_TAG}"

    # Run the push task
    if call_task "release:push:${env}" "" "CLI_ARGS=${TEST_TAG}"; then
        print_status "PASS" "release:push:${env} completed successfully"

        # Check if images were pushed to registry
        sleep 2  # Give registry time to update

        if check_image_in_registry "salesto-php" "${TEST_TAG}" "${REGISTRY_URL}"; then
            print_status "PASS" "salesto-php:${TEST_TAG} pushed to registry successfully"
        else
            print_status "FAIL" "salesto-php:${TEST_TAG} not found in registry"
        fi

        if check_image_in_registry "salesto-http" "${TEST_TAG}" "${REGISTRY_URL}"; then
            print_status "PASS" "salesto-http:${TEST_TAG} pushed to registry successfully"
        else
            print_status "FAIL" "salesto-http:${TEST_TAG} not found in registry"
        fi
    else
        print_status "FAIL" "push:${env} failed"
    fi
}

# Function to test build and push
test_build_and_push() {
    local env=$1
    print_status "INFO" "Testing build-and-push:${env} with tag ${TEST_TAG}"

    # Set environment variables
    export REGISTRY_URL="${REGISTRY_URL}"
    export IMAGE_TAG="${TEST_TAG}"

    # Run the build-and-push task
    if call_task "release:build-and-push:${env}" "" "CLI_ARGS=${TEST_TAG}"; then
        print_status "PASS" "release:build-and-push:${env} completed successfully"

        # Verify images are in registry
        sleep 2

        if check_image_in_registry "salesto-php" "${TEST_TAG}" "${REGISTRY_URL}"; then
            print_status "PASS" "salesto-php:${TEST_TAG} available in registry after build-and-push"
        else
            print_status "FAIL" "salesto-php:${TEST_TAG} not found in registry after build-and-push"
        fi

        if check_image_in_registry "salesto-http" "${TEST_TAG}" "${REGISTRY_URL}"; then
            print_status "PASS" "salesto-http:${TEST_TAG} available in registry after build-and-push"
        else
            print_status "FAIL" "salesto-http:${TEST_TAG} not found in registry after build-and-push"
        fi
    else
        print_status "FAIL" "build-and-push:${env} failed"
    fi
}

# Function to test full release
test_release() {
    print_status "INFO" "Testing full release with tag ${TEST_TAG}"

    # Set environment variables
    export REGISTRY_URL="${REGISTRY_URL}"
    export IMAGE_TAG="${TEST_TAG}"

    # Run the release task
    if call_task "release:release" "" "CLI_ARGS=${TEST_TAG}"; then
        print_status "PASS" "release:release completed successfully"

        # Verify all images are in registry
        sleep 3

        local all_images_present=true

        if check_image_in_registry "salesto-php" "${TEST_TAG}" "${REGISTRY_URL}"; then
            print_status "PASS" "salesto-php:${TEST_TAG} available in registry after release"
        else
            print_status "FAIL" "salesto-php:${TEST_TAG} not found in registry after release"
            all_images_present=false
        fi

        if check_image_in_registry "salesto-http" "${TEST_TAG}" "${REGISTRY_URL}"; then
            print_status "PASS" "salesto-http:${TEST_TAG} available in registry after release"
        else
            print_status "FAIL" "salesto-http:${TEST_TAG} not found in registry after release"
            all_images_present=false
        fi

        if [ "$all_images_present" = true ]; then
            print_status "PASS" "All images successfully released to registry"
        fi
    else
        print_status "FAIL" "release failed"
    fi
}

# Wrapper functions for test cases
test_build_dev() {
    test_build "dev"
}

test_build_prod() {
    test_build "prod"
}

test_push_dev() {
    test_push "dev"
}

test_push_prod() {
    test_push "prod"
}

test_build_and_push_dev() {
    test_build_and_push "dev"
}

test_build_and_push_prod() {
    test_build_and_push "prod"
}

# Main test execution
main() {
    # Initialize test suite
    init_test_suite "Taskfile Release Test Suite" "$TEST_TAG"

    # Register cleanup
    register_cleanup cleanup_release_test

    # Show configuration
    log_info "Registry URL: $REGISTRY_URL"
    log_info "Docker Network: $DOCKER_NETWORK"

    # Check prerequisites
    print_status "INFO" "Checking prerequisites..."
    check_command "docker"
    check_command "task"
    check_command "curl"

    # Check project files
    check_project_file "taskfile.release.yaml"
    check_project_file "Dockerfile"

    print_status "PASS" "All prerequisites met"

    # Create test network
    print_status "INFO" "Creating test network..."
    if docker network create "${DOCKER_NETWORK}" 2>/dev/null; then
        print_status "PASS" "Test network created"
    else
        print_status "WARN" "Test network already exists or failed to create"
    fi

    # Start local Docker registry
    print_status "INFO" "Starting local Docker registry..."
    if docker run -d \
        --name "${REGISTRY_NAME}" \
        --network "${DOCKER_NETWORK}" \
        -p "${REGISTRY_PORT}:5000" \
        registry:2; then
        print_status "PASS" "Registry container started"
    else
        print_status "FAIL" "Failed to start registry container"
        exit 1
    fi

    # Wait for registry to be ready
    wait_for_registry

    # Test individual components
    run_test_case "Build Dev Images" "test_build_dev"
    run_test_case "Build Prod Images" "test_build_prod"

    run_test_case "Push Dev Images" "test_push_dev"
    run_test_case "Push Prod Images" "test_push_prod"

    # Clean up images for next test
    docker rmi "${REGISTRY_URL}/salesto-php:${TEST_TAG}" 2>/dev/null || true
    docker rmi "${REGISTRY_URL}/salesto-http:${TEST_TAG}" 2>/dev/null || true

    run_test_case "Build and Push Dev" "test_build_and_push_dev"
    run_test_case "Build and Push Prod" "test_build_and_push_prod"

    # Clean up images for final test
    docker rmi "${REGISTRY_URL}/salesto-php:${TEST_TAG}" 2>/dev/null || true
    docker rmi "${REGISTRY_URL}/salesto-http:${TEST_TAG}" 2>/dev/null || true

    run_test_case "Full Release" test_release

    # Complete test suite
    log_success "Registry URL: http://${REGISTRY_URL}/v2/_catalog"
    log_success "You can check the registry contents at the URL above"
    complete_test_suite 0
}

# Run main function
main "$@"
