# Taskfile Release Tests

This directory contains test scripts for validating the taskfile release functionality. The tests use Docker-in-Docker (DinD) to build images and push them to a local Docker registry for verification.

## Test Scripts

### 1. `test_release.sh` - Comprehensive Test Suite

The main test script that thoroughly tests all aspects of the release process:

- **Individual build tasks**: Tests `release:build:dev` and `release:build:prod`
- **Individual push tasks**: Tests `release:push:dev` and `release:push:prod`
- **Combined tasks**: Tests `release:build-and-push:dev` and `release:build-and-push:prod`
- **Full release**: Tests the complete `release:release` task
- **Registry verification**: Confirms images are properly pushed to the local registry

**Features:**
- Uses BashTestCase framework for structured testing
- Docker network isolation for better test reliability
- Dynamic test ID generation to avoid conflicts
- Comprehensive error handling and detailed logging
- Individual test case execution with failure tracking
- Automatic cleanup on exit
- Registry health checks and API verification

**Configuration:**
- Registry Port: `5000`
- Container Name: `test-registry`
- Network: `test-network` (isolated)
- Tag Strategy: Dynamic timestamp-based IDs
- Execution Time: ~3-5 minutes (comprehensive)

**Usage:**
```bash
# Run from project root
./tests/taskfile/release/test_release.sh
```

**Best for:** CI/CD pipelines, thorough validation, debugging release issues

### 2. `test_release_simple.sh` - Quick Smoke Test

A simplified test script for fast validation of core functionality:

- Tests only `release:build-and-push:dev` and `release:build-and-push:prod`
- Basic registry verification via API calls
- Minimal setup for faster execution
- Fixed test tag for consistency

**Features:**
- Uses BashTestCase framework
- Streamlined execution flow
- Simple registry setup without networks
- Basic image verification
- Quick feedback for developers

**Configuration:**
- Registry Port: `5001` (different from comprehensive test)
- Container Name: `simple-test-registry`
- Network: None (standalone)
- Tag Strategy: Fixed `simple-test` tag
- Execution Time: ~1-2 minutes (fast)

**Usage:**
```bash
# Run from project root
./tests/taskfile/release/test_release_simple.sh
```

**Best for:** Quick developer validation, pre-commit checks, fast feedback loops

## Test Script Comparison

| Aspect | test_release.sh | test_release_simple.sh |
|--------|----------------|----------------------|
| **Purpose** | Comprehensive testing | Quick smoke test |
| **Test Scope** | Individual + combined tasks | Combined tasks only |
| **Registry Port** | 5000 | 5001 |
| **Network Setup** | Docker network isolation | Standalone registry |
| **Tag Strategy** | Dynamic test IDs | Fixed "simple-test" |
| **Test Cases** | 6 test scenarios | 2 test scenarios |
| **Execution Time** | 3-5 minutes | 1-2 minutes |
| **Use Case** | CI/CD, thorough validation | Developer quick checks |
| **Debugging** | Detailed logs & status | Basic verification |

## Prerequisites

Before running the tests, ensure you have:

1. **Docker** - For building images and running the local registry
2. **Task** - The task runner (install from https://taskfile.dev/)
3. **curl** - For registry API calls (usually pre-installed)

## How the Tests Work

### Test Environment Setup

1. **Local Docker Registry**: Each test starts a local Docker registry container on a unique port
2. **Test Network**: Creates an isolated Docker network for the test
3. **Unique Tags**: Uses timestamp-based tags to avoid conflicts

### Test Process

1. **Build Phase**: 
   - Executes taskfile build commands
   - Verifies images are created locally
   - Checks both `salesto-php` and `salesto-http` images

2. **Push Phase**:
   - Pushes images to the local registry
   - Verifies images are available via registry API

3. **Verification**:
   - Queries registry API to confirm image presence
   - Validates image tags match expected values

### Cleanup

Both scripts automatically clean up:
- Stop and remove registry containers
- Remove test Docker networks
- Clean up built test images

## Understanding the Output

### Status Indicators

- `✓ PASS:` - Test step completed successfully
- `✗ FAIL:` - Test step failed (script will exit)
- `ℹ INFO:` - Informational message
- `⚠ WARN:` - Warning message (non-fatal)

### Example Output

```bash
=== Taskfile Release Test Suite ===
Test Tag: test-1640995200
Registry URL: localhost:5000

ℹ INFO: Checking prerequisites...
✓ PASS: All prerequisites met
ℹ INFO: Starting local Docker registry...
✓ PASS: Registry container started
✓ PASS: Registry is ready

=== Testing Individual Build Tasks ===
ℹ INFO: Testing build:dev with tag test-1640995200
✓ PASS: build:dev completed successfully
✓ PASS: salesto-php:test-1640995200 image built successfully
✓ PASS: salesto-http:test-1640995200 image built successfully
```

## Troubleshooting

### Common Issues

1. **Port Conflicts**: If registry ports are in use, the script will fail. Stop any existing registry containers:
   ```bash
   docker stop test-registry simple-test-registry
   docker rm test-registry simple-test-registry
   ```

2. **Docker Permissions**: Ensure your user has permission to run Docker commands

3. **Task Not Found**: Install Task from https://taskfile.dev/installation/

4. **Build Failures**: Check that all required files exist:
   - `Dockerfile`
   - `taskfile.release.yaml`
   - `docker-compose.dev.yml`
   - `docker-compose.prod.yml`

### Manual Registry Inspection

You can manually inspect the registry contents:

```bash
# For comprehensive test (port 5000)
curl http://localhost:5000/v2/_catalog
curl http://localhost:5000/v2/salesto-php/tags/list
curl http://localhost:5000/v2/salesto-http/tags/list

# For simple test (port 5001)
curl http://localhost:5001/v2/_catalog
curl http://localhost:5001/v2/salesto-php/tags/list
curl http://localhost:5001/v2/salesto-http/tags/list
```

## Integration with CI/CD

These tests can be integrated into CI/CD pipelines:

```yaml
# Example GitHub Actions - Comprehensive testing
- name: Test Taskfile Release (Full)
  run: |
    chmod +x tests/taskfile/release/test_release.sh
    ./tests/taskfile/release/test_release.sh

# Example GitHub Actions - Quick validation
- name: Test Taskfile Release (Quick)
  run: |
    chmod +x tests/taskfile/release/test_release_simple.sh
    ./tests/taskfile/release/test_release_simple.sh
```

**Recommended CI/CD Strategy:**
- Use `test_release_simple.sh` for pull request validation (fast feedback)
- Use `test_release.sh` for main branch validation and releases (thorough testing)

## Test Configuration

The tests use the following configuration:

| Variable | test_release.sh | test_release_simple.sh | Description |
|----------|----------------|----------------------|-------------|
| `REGISTRY_PORT` | 5000 | 5001 | Local registry port |
| `REGISTRY_NAME` | test-registry | simple-test-registry | Registry container name |
| `TEST_TAG` | Dynamic (timestamp-based) | Fixed "simple-test" | Tag for test images |
| `DOCKER_NETWORK` | test-network | None | Isolated network name |
| `REGISTRY_URL` | localhost:5000 | localhost:5001 | Registry endpoint |

## Expected Images

The tests verify the following images are built and pushed:

1. **salesto-php**: PHP-FPM application container
   - Dev target: `dev` stage from Dockerfile
   - Prod target: `prod` stage from Dockerfile

2. **salesto-http**: Nginx web server container
   - Dev target: `http-base` stage from Dockerfile
   - Prod target: `http-prod` stage from Dockerfile

## Contributing

When modifying the taskfile release functionality:

1. Run the comprehensive test suite: `./tests/taskfile/test_release.sh`
2. Ensure all tests pass before committing changes
3. Update tests if new functionality is added
4. Consider adding new test cases for edge cases
