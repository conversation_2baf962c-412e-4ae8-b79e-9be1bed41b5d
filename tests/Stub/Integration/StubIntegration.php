<?php

declare(strict_types=1);

namespace Tests\Stub\Integration;

use Shared\Integration\Abstract\Port\Authorization\AuthorizationAbstract;
use Shared\Integration\Abstract\Port\IntegrationInterface;
use Tests\Stub\Integration\Authorization\StubAuthorization;

class StubIntegration implements IntegrationInterface
{
    public function getName(): string
    {
        return 'Test Integration';
    }

    public function getDescription(): string
    {
        return 'Test Integration for testing purposes';
    }

    public function authorizator(): AuthorizationAbstract
    {
        return new StubAuthorization();
    }
}