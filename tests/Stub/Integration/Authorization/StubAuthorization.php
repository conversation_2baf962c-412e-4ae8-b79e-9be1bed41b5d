<?php

declare(strict_types=1);

namespace Tests\Stub\Integration\Authorization;

use Shared\Integration\Abstract\Port\Authorization\AuthorizationAbstract;
use Shared\Integration\Abstract\Port\Authorization\AuthorizationDataAbstract;
use Shared\Integration\Abstract\Port\Authorization\Method\OAuthCodeFlowAbstractAuthorizationInterface;
use Tests\Spy\SpyTrait;

class StubAuthorization extends AuthorizationAbstract implements OAuthCodeFlowAbstractAuthorizationInterface
{
    use SpyTrait;

    public function authorizationUrl(string $redirectUrl): string
    {
        $this->recordMethodCall('authorizationUrl', ['redirectUrl' => $redirectUrl]);
        
        return 'https://test-auth-url.com/authorize?redirect_uri=' . urlencode($redirectUrl);
    }

    public function exchangeCode(string $code, string $redirectUrl): AuthorizationDataAbstract
    {
        $this->recordMethodCall('exchangeCode', [
            'code' => $code,
            'redirectUrl' => $redirectUrl
        ]);
        
        return new StubAuthorizationData([
            'access_token' => 'test_access_token',
            'refresh_token' => 'test_refresh_token',
            'expires_in' => 3600,
            'token_type' => 'bearer'
        ]);
    }

    public function refreshToken(array $authorizeData): AuthorizationDataAbstract
    {
        $this->recordMethodCall('refreshToken', ['authorizeData' => $authorizeData]);
        
        return new StubAuthorizationData([
            'access_token' => 'refreshed_access_token',
            'refresh_token' => 'refreshed_refresh_token',
            'expires_in' => 3600,
            'token_type' => 'bearer'
        ]);
    }
}