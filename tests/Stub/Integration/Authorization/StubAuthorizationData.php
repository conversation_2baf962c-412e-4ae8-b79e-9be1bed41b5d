<?php

declare(strict_types=1);

namespace Tests\Stub\Integration\Authorization;

use Shared\Integration\Abstract\Port\Authorization\AuthorizationDataAbstract;

class StubAuthorizationData extends AuthorizationDataAbstract
{
    public function __construct(private array $data)
    {
    }

    public function toArray(): array
    {
        return $this->data;
    }

    public static function fromArray(array $data): static
    {
        return new static($data);
    }
}