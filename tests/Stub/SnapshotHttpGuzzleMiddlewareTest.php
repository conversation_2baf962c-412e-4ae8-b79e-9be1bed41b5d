<?php

declare(strict_types=1);

use Guz<PERSON><PERSON>ttp\Client;
use Guz<PERSON><PERSON>ttp\Handler\MockHandler;
use Guzzle<PERSON>ttp\HandlerStack;
use GuzzleHttp\Psr7\Response;
use Tests\Stub\SnapshotHttpGuzzleMiddleware;

// Create a temporary directory for snapshots
beforeEach(function () {
    $this->snapshotDir = sys_get_temp_dir() . '/snapshot_test_' . uniqid();
    mkdir($this->snapshotDir, 0755, true);
});

// Clean up after tests
afterEach(function () {
    $snapshotsDir = $this->snapshotDir . '/.snapshots';
    if (is_dir($snapshotsDir)) {
        array_map('unlink', glob("$snapshotsDir/*"));
        rmdir($snapshotsDir);
    }
    rmdir($this->snapshotDir);
});

describe('SnapshotHttpGuzzleMiddleware', function () {
    it('creates and uses snapshots', function () {
        // Create a mock handler with a test response
        $mock = new MockHandler([
            new Response(200, ['Content-Type' => 'application/json'], '{"name": "<PERSON>", "age": 30}'),
        ]);
        
        // Create a handler stack with the mock handler
        $stack = HandlerStack::create($mock);
        
        // Add the snapshot middleware
        $stack->push(SnapshotHttpGuzzleMiddleware::create(
            snapshotStoragePath: $this->snapshotDir,
            snapshotName: 'test-snapshot'
        ));
        
        // Create a client with the handler stack
        $client = new Client(['handler' => $stack]);
        
        // Make a request - this will use the mock and create a snapshot
        $response1 = $client->get('https://example.com/api/users/1');

        // Verify the response
        expect($response1->getStatusCode())->toBe(200);
        $bodyContents = $response1->getBody()->getContents();
        $data1 = json_decode($bodyContents, true);
        expect($data1)->toBe(['name' => 'John Doe', 'age' => 30]);
        
        // Verify the snapshot was created
        $snapshotPath = $this->snapshotDir . '/.snapshots/test-snapshot.txt';
        expect(file_exists($snapshotPath))->toBeTrue();
        
        // Create a new client with an empty mock handler (which would fail if called)
        // This tests that the snapshot is used instead of making a real request
        $emptyMock = new MockHandler([]);
        $newStack = HandlerStack::create($emptyMock);
        $newStack->push(SnapshotHttpGuzzleMiddleware::create(
            snapshotStoragePath: $this->snapshotDir,
            snapshotName: 'test-snapshot'
        ));
        $newClient = new Client(['handler' => $newStack]);
        
        // Make the same request - this should use the snapshot
        $response2 = $newClient->get('https://example.com/api/users/1');
        
        // Verify the response from the snapshot
        expect($response2->getStatusCode())->toBe(200);
        $bodyContents2 = $response2->getBody()->getContents();
        $data2 = json_decode($bodyContents2, true);
        expect($data2)->toBe(['name' => 'John Doe', 'age' => 30]);
    });
    
    it('generates snapshot names based on the request when no name is provided', function () {
        // Create a mock handler with a test response
        $mock = new MockHandler([
            new Response(200, [], 'Response content'),
        ]);
        
        // Create a handler stack with the mock handler
        $stack = HandlerStack::create($mock);
        
        // Add the snapshot middleware without a name
        $stack->push(SnapshotHttpGuzzleMiddleware::create(
            snapshotStoragePath: $this->snapshotDir
        ));
        
        // Create a client with the handler stack
        $client = new Client(['handler' => $stack]);
        
        // Make a request
        $client->get('https://example.com/api/users/123');
        
        // Verify a snapshot was created with an auto-generated name
        $snapshotsDir = $this->snapshotDir . '/.snapshots';
        $files = glob("$snapshotsDir/*");
        expect(count($files))->toBe(1);
        
        // The filename should contain the method and path
        $filename = basename($files[0]);
        expect($filename)->toContain('GET');
        expect($filename)->toContain('api_users_123');
    });
    
    it('handles different requests with different snapshots', function () {
        // Create a mock handler with different responses
        $mock = new MockHandler([
            new Response(200, [], 'User 1 data'),
            new Response(200, [], 'User 2 data'),
        ]);
        
        // Create a handler stack with the mock handler
        $stack = HandlerStack::create($mock);
        
        // Add the snapshot middleware
        $stack->push(SnapshotHttpGuzzleMiddleware::create(
            snapshotStoragePath: $this->snapshotDir
        ));
        
        // Create a client with the handler stack
        $client = new Client(['handler' => $stack]);
        
        // Make different requests
        $response1 = $client->get('https://example.com/api/users/1');
        $response2 = $client->get('https://example.com/api/users/2');
        
        // Verify the responses
        $bodyContents1 = $response1->getBody()->getContents();
        $bodyContents2 = $response2->getBody()->getContents();
        expect($bodyContents1)->toBe('User 1 data');
        expect($bodyContents2)->toBe('User 2 data');
        
        // Verify two different snapshots were created
        $snapshotsDir = $this->snapshotDir . '/.snapshots';
        $files = glob("$snapshotsDir/*");
        expect(count($files))->toBe(2);
    });
});