# SnapshotHttpGuzzleMiddleware

A Guzzle middleware for recording and replaying HTTP responses in tests.

## Overview

The `SnapshotHttpGuzzleMiddleware` provides a way to capture HTTP responses during test execution and replay them in subsequent test runs. This is particularly useful for:

1. **Speeding up tests** by avoiding actual HTTP requests
2. **Making tests more reliable** by eliminating dependency on external services
3. **Testing without an internet connection**
4. **Ensuring consistent test results** regardless of external API changes

## Installation

The `SnapshotHttpGuzzleMiddleware` is already included in the project. You can find it at `tests/Stub/SnapshotHttpGuzzleMiddleware.php`.

## Usage

### Basic Usage

```php
use GuzzleHttp\Client;
use GuzzleHttp\HandlerStack;
use Tests\Stub\SnapshotHttpGuzzleMiddleware;

// Create a handler stack
$stack = HandlerStack::create();

// Add the snapshot middleware
$stack->push(SnapshotHttpGuzzleMiddleware::create(
    snapshotStoragePath: __DIR__ . '/snapshots',
    snapshotName: 'my-api-call'
));

// Create a client with the handler stack
$client = new Client([
    'handler' => $stack,
    'base_uri' => 'https://api.example.com',
]);

// Make a request - it will be recorded the first time
// and replayed from the snapshot in subsequent runs
$response = $client->get('/endpoint');
```

### Custom Snapshot Storage Path

By default, snapshots are stored in a `.snapshots` directory under the specified storage path. You can customize the storage path:

```php
$stack->push(SnapshotHttpGuzzleMiddleware::create(
    snapshotStoragePath: __DIR__ . '/custom/path',
    snapshotName: 'my-api-call'
));
```

### Custom Snapshot Naming

You can provide a custom name for the snapshot file:

```php
$stack->push(SnapshotHttpGuzzleMiddleware::create(
    snapshotName: 'user-profile-api-response'
));
```

If you don't provide a name, the middleware will generate one based on the request method, path, and a hash of the request:

```php
// For a GET request to /users/123
// The generated name might be something like: GET_users_123_a1b2c3d4e5f6.txt
```

### Integration with PHPUnit/Pest

```php
use GuzzleHttp\Client;
use GuzzleHttp\HandlerStack;
use Tests\Stub\SnapshotHttpGuzzleMiddleware;

class ApiTest extends TestCase
{
    protected function setUp(): void
    {
        parent::setUp();
        
        $stack = HandlerStack::create();
        $stack->push(SnapshotHttpGuzzleMiddleware::create(
            snapshotStoragePath: __DIR__ . '/snapshots',
            snapshotName: 'api-test'
        ));
        
        $this->client = new Client([
            'handler' => $stack,
            'base_uri' => 'https://api.example.com',
        ]);
    }
    
    public function testApiCall()
    {
        $response = $this->client->get('/users/123');
        $this->assertEquals(200, $response->getStatusCode());
        
        $data = json_decode($response->getBody(), true);
        $this->assertEquals('John Doe', $data['name']);
    }
}
```

### Integration with Laravel

```php
use GuzzleHttp\Client;
use GuzzleHttp\HandlerStack;
use Tests\Stub\SnapshotHttpGuzzleMiddleware;

// In a service provider or bootstrap file
$this->app->bind(Client::class, function ($app) {
    $stack = HandlerStack::create();
    $stack->push(SnapshotHttpGuzzleMiddleware::create(
        snapshotStoragePath: storage_path('app/snapshots'),
        snapshotName: null // Auto-generate names based on requests
    ));
    
    return new Client([
        'handler' => $stack,
        'base_uri' => config('services.api.base_uri'),
    ]);
});
```

## How It Works

1. **First Test Run**:
   - When a request is made, the middleware checks if a snapshot exists for that request
   - If no snapshot exists, the request is passed to the next handler in the stack
   - The response is captured and saved as a snapshot file

2. **Subsequent Test Runs**:
   - When the same request is made, the middleware finds the existing snapshot
   - Instead of making a real HTTP request, it returns the recorded response
   - This makes tests faster and more reliable

## Snapshot File Format

Snapshots are stored as serialized PHP arrays containing:

1. **content**: The response body content
2. **response**: The full PSR-7 response object

## Advanced Usage

### Refreshing Snapshots

To refresh snapshots (force new HTTP requests), simply delete the snapshot files:

```php
// In your test setup
if ($refreshSnapshots) {
    $snapshotDir = __DIR__ . '/snapshots/.snapshots';
    if (is_dir($snapshotDir)) {
        array_map('unlink', glob("$snapshotDir/*"));
    }
}
```

### Conditional Snapshot Usage

You can conditionally enable/disable the middleware:

```php
if (getenv('USE_SNAPSHOTS') === 'true') {
    $stack->push(SnapshotHttpGuzzleMiddleware::create());
}
```

### Using with Mock Responses

You can combine this middleware with Guzzle's MockHandler for complete control:

```php
use GuzzleHttp\Handler\MockHandler;
use GuzzleHttp\HandlerStack;
use GuzzleHttp\Psr7\Response;
use Tests\Stub\SnapshotHttpGuzzleMiddleware;

// Create a mock handler
$mock = new MockHandler([
    new Response(200, [], '{"result": "success"}'),
]);

// Create a handler stack with the mock handler
$stack = HandlerStack::create($mock);

// Add the snapshot middleware
$stack->push(SnapshotHttpGuzzleMiddleware::create());

// The first run will use the mock response and save it as a snapshot
// Subsequent runs will use the snapshot
```

## Limitations

1. **Binary Responses**: The middleware may not handle binary responses (like file downloads) correctly in all cases.
2. **Streaming Responses**: Streaming responses are fully buffered before being saved.
3. **Request Matching**: Requests are matched based on method, path, and a hash of the request, which may not capture all variations.

## Example

Here's a complete example of using the middleware in a test:

```php
use GuzzleHttp\Client;
use GuzzleHttp\HandlerStack;
use Tests\Stub\SnapshotHttpGuzzleMiddleware;

// Create a handler stack
$stack = HandlerStack::create();

// Add the snapshot middleware
$stack->push(SnapshotHttpGuzzleMiddleware::create(
    snapshotStoragePath: __DIR__ . '/snapshots',
    snapshotName: 'weather-api'
));

// Create a client with the handler stack
$client = new Client([
    'handler' => $stack,
    'base_uri' => 'https://api.weatherapi.com/v1',
]);

// Make a request - first time will hit the real API
// Subsequent runs will use the snapshot
$response = $client->get('/current.json', [
    'query' => [
        'key' => 'your-api-key',
        'q' => 'London',
    ]
]);

$data = json_decode($response->getBody(), true);
echo "Current temperature in London: {$data['current']['temp_c']}°C\n";
```