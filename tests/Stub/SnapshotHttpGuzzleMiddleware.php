<?php

declare(strict_types=1);

namespace Tests\Stub;

use GuzzleHttp\Promise\Create;
use Guz<PERSON>Http\Promise\PromiseInterface;
use GuzzleHttp\Psr7\Utils;
use Psr\Http\Message\RequestInterface;
use Psr\Http\Message\ResponseInterface;

class SnapshotHttpGuzzleMiddleware
{
    /**
     * @var callable(RequestInterface, array): PromiseInterface
     */
    private $nextHandler;

    /**
     * @param callable(RequestInterface, array): PromiseInterface $nextHandler
     */
    public function __construct(
        private string $snapshotStoragePath,
        private ?string $snapshotName,
        callable $nextHandler,
    ) {
        $this->nextHandler = $nextHandler;
    }

    public static function create(
        string $snapshotStoragePath = __DIR__,
        ?string $snapshotName = null,
    ): callable {
        return static function (callable $handler) use ($snapshotStoragePath, $snapshotName): SnapshotHttpGuzzleMiddleware {
            return new SnapshotHttpGuzzleMiddleware($snapshotStoragePath, $snapshotName, $handler);
        };
    }

    public function __invoke(RequestInterface $request, array $options): PromiseInterface
    {
        $name = $this->createSnapshotName($request, 'txt');
        $path = $this->createSnapshotPath($name);
        $snapshot = $this->getSnapshot($path);

        if ($snapshot) {
            return $snapshot;
        }

        $promise = ($this->nextHandler)($request, $options);

        $this->makeSnapshot($path, $promise);

        return $promise;
    }

    private function createSnapshotName(RequestInterface $request, string $extension): string
    {
        if ($this->snapshotName !== null) {
            return implode('', [
                $this->snapshotName,
                '.',
                trim($extension, '.')
            ]);
        }

        return implode('', [
            $request->getMethod(),
            str_replace('/', '_', $request->getUri()->getPath()),
            sha1(serialize($request)),
            '.',
            trim($extension, '.')
        ]);
    }

    private function getSnapshot(
        string $snapshotFilename,
    ): ?PromiseInterface {
        $data = @file_get_contents($snapshotFilename);

        if (false === $data) {
            return null;
        }

        /** @var array{'promise': PromiseInterface } $deserialized */
        $deserialized = unserialize($data);

        if (is_array($deserialized)) {
            $content = $deserialized['content'];
            $response = $deserialized['response'];

            $stream = Utils::streamFor($content);
            $response = $response->withBody($stream);
            return Create::promiseFor($response);
        }

        return null;
    }

    private function makeSnapshot(
        string $snapshotFilename,
        PromiseInterface $promise
    ): void {
        if (!file_exists(dirname($snapshotFilename))) {
            mkdir(dirname($snapshotFilename), 0755, true);
        }

        $promise->then(function (ResponseInterface $response) use ($snapshotFilename, $promise): void {
            // Get the content and rewind the body
            $content = $response->getBody()->getContents();
            $response->getBody()->rewind();
            
            file_put_contents($snapshotFilename, serialize([
                'content' => $content,
                'response' => $response,
            ]));
        });
    }

    private function createSnapshotPath(string $snapshotFilename): string
    {
        $paths = array_map(
            fn($s) => rtrim($s, '/'),
            [
                $this->snapshotStoragePath,
                '.snapshots',
                $snapshotFilename
            ],
        );

        return implode(DIRECTORY_SEPARATOR, $paths);
    }
}
