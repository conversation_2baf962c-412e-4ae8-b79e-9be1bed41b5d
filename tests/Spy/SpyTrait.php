<?php

declare(strict_types=1);

namespace Tests\Spy;

trait SpyTrait
{
    /**
     * @var array<string, array<array>>
     */
    protected array $methodCalls = [];

    /**
     * Record a method call with its arguments
     */
    protected function recordMethodCall(string $method, array $arguments = []): void
    {
        if (!isset($this->methodCalls[$method])) {
            $this->methodCalls[$method] = [];
        }

        $this->methodCalls[$method][] = $arguments;
    }

    /**
     * Get all recorded calls for a specific method
     * 
     * @return array<array>
     */
    public function getMethodCalls(string $method): array
    {
        return $this->methodCalls[$method] ?? [];
    }

    /**
     * Check if a method was called
     */
    public function wasMethodCalled(string $method): bool
    {
        return isset($this->methodCalls[$method]) && !empty($this->methodCalls[$method]);
    }

    /**
     * Get the number of times a method was called
     */
    public function getMethodCallCount(string $method): int
    {
        return count($this->getMethodCalls($method));
    }

    /**
     * Check if a method was called with specific arguments
     */
    public function wasMethodCalledWith(string $method, array $expectedArguments): bool
    {
        $calls = $this->getMethodCalls($method);
        
        foreach ($calls as $arguments) {
            $match = true;
            
            foreach ($expectedArguments as $key => $value) {
                if (!isset($arguments[$key]) || $arguments[$key] !== $value) {
                    $match = false;
                    break;
                }
            }
            
            if ($match) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * Get the arguments from the last call to a method
     */
    public function getLastMethodCallArguments(string $method): ?array
    {
        $calls = $this->getMethodCalls($method);
        
        if (empty($calls)) {
            return null;
        }
        
        return end($calls);
    }

    /**
     * Reset all recorded method calls
     */
    public function resetMethodCalls(): void
    {
        $this->methodCalls = [];
    }
}