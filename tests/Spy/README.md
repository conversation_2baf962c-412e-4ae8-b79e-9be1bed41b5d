# SpyTrait

A simple trait for spying on method calls in tests.

## Overview

The `SpyTrait` provides a way to record and verify method calls in test classes. It's particularly useful for testing that methods are called with the expected parameters without having to rely on complex mocking frameworks.

## Installation

The `SpyTrait` is already included in the project. You can find it at `tests/Spy/SpyTrait.php`.

## Usage

### Adding the SpyTrait to a Test Class

```php
use Tests\Spy\SpyTrait;

class MyTestClass
{
    use SpyTrait;
    
    public function methodToTest(string $param1, int $param2): void
    {
        // Record the method call
        $this->recordMethodCall('methodToTest', [
            'param1' => $param1,
            'param2' => $param2
        ]);
        
        // Method implementation...
    }
}
```

### Recording Method Calls

To record a method call, use the `recordMethodCall` method:

```php
$this->recordMethodCall('methodName', [
    'paramName1' => $paramValue1,
    'paramName2' => $paramValue2
]);
```

### Verifying Method Calls

#### Check if a Method Was Called

```php
if ($object->wasMethodCalled('methodName')) {
    // Method was called
}
```

#### Get the Number of Times a Method Was Called

```php
$callCount = $object->getMethodCallCount('methodName');
```

#### Check if a Method Was Called with Specific Arguments

```php
if ($object->wasMethodCalledWith('methodName', [
    'paramName1' => 'expectedValue1'
])) {
    // Method was called with the expected parameter
}
```

#### Get All Calls for a Method

```php
$calls = $object->getMethodCalls('methodName');
foreach ($calls as $call) {
    $param1 = $call['paramName1'];
    $param2 = $call['paramName2'];
    // ...
}
```

#### Get the Arguments from the Last Call to a Method

```php
$lastCall = $object->getLastMethodCallArguments('methodName');
if ($lastCall !== null) {
    $param1 = $lastCall['paramName1'];
    $param2 = $lastCall['paramName2'];
    // ...
}
```

#### Reset All Recorded Method Calls

```php
$object->resetMethodCalls();
```

## Example

Here's a complete example of how to use the `SpyTrait` in a test:

```php
use Tests\Spy\SpyTrait;

class UserService
{
    use SpyTrait;
    
    public function createUser(string $name, string $email): void
    {
        $this->recordMethodCall('createUser', [
            'name' => $name,
            'email' => $email
        ]);
        
        // Implementation...
    }
}

// In a test
$userService = new UserService();
$userService->createUser('John Doe', '<EMAIL>');

// Verify the method was called
expect($userService->wasMethodCalled('createUser'))->toBeTrue();

// Verify the method was called with the expected parameters
expect($userService->wasMethodCalledWith('createUser', [
    'name' => 'John Doe'
]))->toBeTrue();

// Get the last call arguments
$lastCall = $userService->getLastMethodCallArguments('createUser');
expect($lastCall['email'])->toBe('<EMAIL>');
```

## Testing

The `SpyTrait` itself is tested in `tests/Feature/Spy/SpyTraitTest.php` using Pest.