<?php

declare(strict_types=1);

use Tests\Spy\SpyTrait;

// Create a test class that uses the SpyTrait
class TestSpyClass
{
    use SpyTrait;

    public function methodWithNoArgs(): string
    {
        $this->recordMethodCall('methodWithNoArgs');
        return 'result';
    }

    public function methodWithArgs(string $arg1, int $arg2, array $arg3): array
    {
        $this->recordMethodCall('methodWithArgs', [
            'arg1' => $arg1,
            'arg2' => $arg2,
            'arg3' => $arg3
        ]);
        return ['result' => true];
    }

    // Expose protected method for testing
    public function recordCall(string $method, array $arguments = []): void
    {
        $this->recordMethodCall($method, $arguments);
    }
}

// Create a fresh spy instance for each test
function createSpy(): TestSpyClass
{
    return new TestSpyClass();
}

describe('SpyTrait', function () {
    it('records method calls', function () {
        $spy = createSpy();

        // Call method with no arguments
        $spy->methodWithNoArgs();

        // Call method with arguments
        $spy->methodWithArgs('test', 123, ['a' => 'b']);

        // Verify calls were recorded
        expect($spy->wasMethodCalled('methodWithNoArgs'))->toBeTrue();
        expect($spy->wasMethodCalled('methodWithArgs'))->toBeTrue();

        // Verify call counts
        expect($spy->getMethodCallCount('methodWithNoArgs'))->toBe(1);
        expect($spy->getMethodCallCount('methodWithArgs'))->toBe(1);

        // Verify non-existent method
        expect($spy->wasMethodCalled('nonExistentMethod'))->toBeFalse();
        expect($spy->getMethodCallCount('nonExistentMethod'))->toBe(0);
    });

    it('retrieves method calls', function () {
        $spy = createSpy();

        // No calls yet
        expect($spy->getMethodCalls('methodWithArgs'))->toBeEmpty();

        // Make some calls
        $spy->methodWithArgs('first', 1, ['a']);
        $spy->methodWithArgs('second', 2, ['b']);

        // Get all calls
        $calls = $spy->getMethodCalls('methodWithArgs');

        // Verify calls
        expect($calls)->toHaveCount(2);
        expect($calls[0]['arg1'])->toBe('first');
        expect($calls[0]['arg2'])->toBe(1);
        expect($calls[0]['arg3'])->toBe(['a']);
        expect($calls[1]['arg1'])->toBe('second');
        expect($calls[1]['arg2'])->toBe(2);
        expect($calls[1]['arg3'])->toBe(['b']);
    });

    it('checks if method was called with specific arguments', function () {
        $spy = createSpy();

        // Make a call
        $spy->methodWithArgs('test', 123, ['key' => 'value']);

        // Verify exact match
        expect($spy->wasMethodCalledWith('methodWithArgs', [
            'arg1' => 'test',
            'arg2' => 123,
            'arg3' => ['key' => 'value']
        ]))->toBeTrue();

        // Verify partial match
        expect($spy->wasMethodCalledWith('methodWithArgs', [
            'arg1' => 'test'
        ]))->toBeTrue();

        expect($spy->wasMethodCalledWith('methodWithArgs', [
            'arg2' => 123
        ]))->toBeTrue();

        // Verify non-match
        expect($spy->wasMethodCalledWith('methodWithArgs', [
            'arg1' => 'wrong'
        ]))->toBeFalse();

        expect($spy->wasMethodCalledWith('methodWithArgs', [
            'arg2' => 456
        ]))->toBeFalse();

        // Verify non-existent argument
        expect($spy->wasMethodCalledWith('methodWithArgs', [
            'nonExistentArg' => 'value'
        ]))->toBeFalse();

        // Verify non-existent method
        expect($spy->wasMethodCalledWith('nonExistentMethod', [
            'arg1' => 'test'
        ]))->toBeFalse();
    });

    it('retrieves last method call arguments', function () {
        $spy = createSpy();

        // No calls yet
        expect($spy->getLastMethodCallArguments('methodWithArgs'))->toBeNull();

        // Make some calls
        $spy->methodWithArgs('first', 1, ['a']);
        $spy->methodWithArgs('second', 2, ['b']);
        $spy->methodWithArgs('third', 3, ['c']);

        // Get last call
        $lastCall = $spy->getLastMethodCallArguments('methodWithArgs');

        // Verify last call
        expect($lastCall['arg1'])->toBe('third');
        expect($lastCall['arg2'])->toBe(3);
        expect($lastCall['arg3'])->toBe(['c']);
    });

    it('resets method calls', function () {
        $spy = createSpy();

        // Make some calls
        $spy->methodWithNoArgs();
        $spy->methodWithArgs('test', 123, ['a' => 'b']);

        // Verify calls were recorded
        expect($spy->wasMethodCalled('methodWithNoArgs'))->toBeTrue();
        expect($spy->wasMethodCalled('methodWithArgs'))->toBeTrue();

        // Reset calls
        $spy->resetMethodCalls();

        // Verify calls were reset
        expect($spy->wasMethodCalled('methodWithNoArgs'))->toBeFalse();
        expect($spy->wasMethodCalled('methodWithArgs'))->toBeFalse();
        expect($spy->getMethodCallCount('methodWithNoArgs'))->toBe(0);
        expect($spy->getMethodCallCount('methodWithArgs'))->toBe(0);
    });

    it('handles multiple calls with same arguments', function () {
        $spy = createSpy();

        // Make multiple calls with the same arguments
        $spy->methodWithArgs('same', 123, ['a']);
        $spy->methodWithArgs('same', 123, ['a']);
        $spy->methodWithArgs('same', 123, ['a']);

        // Verify call count
        expect($spy->getMethodCallCount('methodWithArgs'))->toBe(3);

        // Verify wasMethodCalledWith still works
        expect($spy->wasMethodCalledWith('methodWithArgs', [
            'arg1' => 'same',
            'arg2' => 123,
            'arg3' => ['a']
        ]))->toBeTrue();
    });

    it('allows direct recording of method calls', function () {
        $spy = createSpy();

        // Directly record a method call
        $spy->recordCall('customMethod', [
            'customArg1' => 'value1',
            'customArg2' => 'value2'
        ]);

        // Verify call was recorded
        expect($spy->wasMethodCalled('customMethod'))->toBeTrue();
        expect($spy->getMethodCallCount('customMethod'))->toBe(1);

        // Verify arguments
        $args = $spy->getLastMethodCallArguments('customMethod');
        expect($args['customArg1'])->toBe('value1');
        expect($args['customArg2'])->toBe('value2');
    });
});
