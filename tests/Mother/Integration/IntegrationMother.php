<?php

declare(strict_types=1);

namespace Tests\Mother\Integration;

use Integration\Domain\Integration;
use Integration\Domain\Provider;
use Tests\Stub\Integration\StubIntegration;

class IntegrationMother
{
    public static function createWithProvider(
        string $name = 'Test Integration',
        string $manifest = StubIntegration::class
    ): Integration {
        $provider = Provider::factory()->create([
            'name' => 'Stub Provider',
            'manifest' => $manifest
        ]);

        return Integration::factory()->create([
            'name' => $name,
            'provider_id' => $provider->id
        ]);
    }
}
