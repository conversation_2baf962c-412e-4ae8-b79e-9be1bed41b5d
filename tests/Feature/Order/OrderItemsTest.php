<?php

declare(strict_types=1);

use Order\Domain\Order;
use Order\Domain\OrderItem;
use Order\UI\Filament\OrderResource\Pages\EditOrder;
use Order\UI\Filament\OrderResource\RelationManagers\OrderItemsRelationManager;

use function Pest\Livewire\livewire;

describe('Order items', function () {
    it('can render relation manager', function () {
        // Arrange
        $order = Order::factory()
            ->withItem(name: 'Item 1', price: '10.00', quantity: 1)
            ->withItem(name: 'Item 2', price: '20.00', quantity: 2)
            ->create();

        // Act Assert
        livewire(OrderItemsRelationManager::class, [
            'ownerRecord' => $order,
            'pageClass' => EditOrder::class,
        ])
            ->assertSuccessful();
    });

    it('can list order items', function () {
        // Arrange
        $order = Order::factory()
            ->withItem(name: 'Item 1', price: '10.00', quantity: 1)
            ->withItem(name: 'Item 2', price: '20.00', quantity: 2)
            ->withItem(name: 'Item 3', price: '30.00', quantity: 3)
            ->create();

        // Act Assert
        livewire(OrderItemsRelationManager::class, [
            'ownerRecord' => $order,
            'pageClass' => EditOrder::class,
        ])
            ->assertCanSeeTableRecords($order->items);
    });

    it('can create order item', function () {
        // Arrange
        $order = Order::factory()->create();
        $initialCount = $order->items()->count();

        // Act
        livewire(OrderItemsRelationManager::class, [
            'ownerRecord' => $order,
            'pageClass' => EditOrder::class,
        ])
            ->callTableAction('create', data: [
                'name' => 'Test Product',
                'price' => '100.00',
                'quantity' => 2,
                'sku' => 'TEST-SKU-123',
            ]);

        // Assert
        expect($order->items()->count())->toBe($initialCount + 1);
        $newItem = $order->items()->latest('id')->first();
        expect($newItem->name)->toBe('Test Product');
        expect($newItem->price)->toBe('100');
        expect($newItem->quantity)->toBe(2);
        expect($newItem->sku)->toBe('TEST-SKU-123');
    });

    it('can edit order item', function () {
        // Arrange
        $order = Order::factory()
            ->withItem(
                name: 'Original Name',
                price: '50.00',
                quantity: 1
            )
            ->create();
        $item = $order->items->first();

        //Act
        livewire(OrderItemsRelationManager::class, [
            'ownerRecord' => $order,
            'pageClass' => EditOrder::class,
        ])
            ->callTableAction('edit', $item, data: [
                'name' => 'Updated Name',
                'price' => '75.00',
                'quantity' => 3,
                'version' => $order->getVersion(),
            ]);

        // Assert
        $item->refresh();
        expect($item->name)->toBe('Updated Name');
        expect($item->price)->toBe('75');
        expect($item->quantity)->toBe(3);
        expect($item->total)->toBe('225'); // 75 * 3
    });

    it('can duplicate order item', function () {
        // Arrange
        $order = Order::factory()
            ->withItem(
                name: 'Original Item',
                price: '50.00',
                quantity: 1
            )
            ->create();

        $item = $order->items->first();
        $initialCount = $order->items()->count();

        // Act
        livewire(OrderItemsRelationManager::class, [
            'ownerRecord' => $order,
            'pageClass' => EditOrder::class,
        ])->callTableAction('duplicate_item', $item);

        // Assert
        expect($order->items()->count())->toBe($initialCount + 1);
    });

    it('can delete order item', function () {
        // Arrange
        $order = Order::factory()
            ->withItem(
                name: 'Item to Delete',
                price: '25.00',
                quantity: 1
            )
            ->create();
        $item = $order->items->first();
        $initialCount = $order->items()->count();

        // Act
        livewire(OrderItemsRelationManager::class, [
            'ownerRecord' => $order,
            'pageClass' => EditOrder::class,
        ])
            ->callTableAction('delete', $item);

        // Assert
        expect($order->items()->count())->toBe($initialCount - 1);
        expect(OrderItem::find($item->id))->toBeNull();
    });

    it('can add multiple items using table action', function () {
        // Arrange
        $order = Order::factory()->create();
        $initialCount = $order->items()->count();

        $itemsData = [
            'items' => [
                '0' => [
                    'name' => 'Action Item 1',
                    'price' => '50.00',
                    'quantity' => 2,
                    'sku' => 'ACTION-SKU-1',
                    'total' => '100.00'
                ],
                '1' => [
                    'name' => 'Action Item 2',
                    'price' => '75.00',
                    'quantity' => 3,
                    'sku' => 'ACTION-SKU-2',
                    'total' => '225.00'
                ]
            ]
        ];

        $livewire = livewire(OrderItemsRelationManager::class, [
            'ownerRecord' => $order,
            'pageClass' => EditOrder::class,
        ]);

        // Act
        $livewire->mountTableAction('add_multiple_items');
        $livewire->set('mountedTableActionsData', []);
        $livewire->setTableActionData($itemsData);
        $livewire->callMountedTableAction();

        // Assert
        expect($order->items()->count())->toBe($initialCount + 2);
    });

//    it('validates order item data', function (array $data, array $expectedErrors) {
//        // Arrange
//        $order = Order::factory()->create();
//
//        // Act
//        $livewire = livewire(OrderItemsRelationManager::class, [
//            'ownerRecord' => $order,
//            'pageClass' => EditOrder::class,
//        ]);
//
//        $livewire->callTableAction('create', data: $data);
//
//        // Assert
//        $livewire->assertHasErrors($expectedErrors);
//    })->with([
//        [['name' => '', 'price' => '10.00', 'quantity' => 1], ['name']], // Missing name
//        [['name' => 'Test Product', 'price' => '-10.00', 'quantity' => 1], ['price']], // Negative price
//        [['name' => 'Test Product', 'price' => '10.00', 'quantity' => 0], ['quantity']], // Zero quantity
//        [['name' => 'Test Product', 'price' => 'not-a-number', 'quantity' => 1], ['price']], // Non-numeric price
//    ]);
//
//    it('handles concurrent modifications', function () {
//        // Arrange
//        $order = Order::factory()
//            ->withItem(name: 'Concurrent Item', price: '50.00', quantity: 1)
//            ->create();
//        $item = $order->items->first();
//
//        // Simulate concurrent modification
//        $livewire1 = livewire(OrderItemsRelationManager::class, [
//            'ownerRecord' => $order,
//            'pageClass' => EditOrder::class,
//        ]);
//
//        $livewire2 = livewire(OrderItemsRelationManager::class, [
//            'ownerRecord' => $order,
//            'pageClass' => EditOrder::class,
//        ]);
//
//        // Act
//        $livewire1->callTableAction('edit', $item, data: [
//            'name' => 'Updated Name 1',
//            'price' => '60.00',
//            'quantity' => 2,
//            'version' => $order->getVersion(),
//        ]);
//
//        $livewire2->callTableAction('edit', $item, data: [
//            'name' => 'Updated Name 2',
//            'price' => '70.00',
//            'quantity' => 3,
//            'version' => $order->getVersion(),
//        ]);
//
//        // Assert
//        $item->refresh();
//        expect($item->name)->toBeOneOf(['Updated Name 1', 'Updated Name 2']);
//    });
//
//    it('restricts unauthorized access', function () {
//        // Arrange
//        $order = Order::factory()->create();
//
//        // Simulate unauthorized user
//        $livewire = livewire(OrderItemsRelationManager::class, [
//            'ownerRecord' => $order,
//            'pageClass' => EditOrder::class,
//        ])->actingAs(User::factory()->create(['role' => 'guest']));
//
//        // Act
//        $livewire->callTableAction('create', data: [
//            'name' => 'Unauthorized Item',
//            'price' => '100.00',
//            'quantity' => 1,
//        ]);
//
//        // Assert
//        $livewire->assertForbidden();
//    });
//
//    it('handles unexpected errors gracefully', function () {
//        // Arrange
//        $order = Order::factory()->create();
//
//        // Simulate error
//        $livewire = livewire(OrderItemsRelationManager::class, [
//            'ownerRecord' => $order,
//            'pageClass' => EditOrder::class,
//        ]);
//
//        // Mock an exception
//        $livewire->callTableAction('create', data: [
//            'name' => 'Error Item',
//            'price' => '100.00',
//            'quantity' => 1,
//        ])->throw(new Exception('Unexpected error'));
//
//        // Assert
//        $livewire->assertSee('An unexpected error occurred. Please try again later.');
//    });
});
