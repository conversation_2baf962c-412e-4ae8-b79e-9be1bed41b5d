<?php

declare(strict_types=1);

use Integration\Application\IntegrationService;
use Integration\Domain\Integration;
use Integration\Domain\Provider;
use Integration\UI\Filament\IntegrationCluster\Resources\IntegrationResource;
use Integration\UI\Filament\IntegrationCluster\Resources\IntegrationResource\Pages\EditIntegration;
use Integration\UI\Filament\IntegrationCluster\Resources\ProviderResource\Pages\ListProviders;
use Tests\Mother\Integration\IntegrationMother;

use function Pest\Livewire\livewire;

describe('Installation of a provider', function () {
    it('installs integration action', function () {
        $provider = Provider::factory()->create();
        $integrationName = 'Test Integration';
        $countBefore = Integration::count();

        livewire(ListProviders::class)
            ->mountTableAction('install', $provider)
            ->assertTableActionDataSet([
                'name' => $provider->name,
            ])
            ->setTableActionData([
                'name' => $integrationName,
            ])
            ->callMountedTableAction()
            ->assertHasNoActionErrors();

        $this->assertDatabaseCount('integration.integrations', $countBefore + 1);
        $this->assertDatabaseHas('integration.integrations', [
            'name' => $integrationName,
            'provider_id' => $provider->id
        ]);
    });
});

describe('Authorization of a integration', function () {
    describe('OAuth Code Flow', function () {
        it('redirect to authorization url', function () {
            // Arrange
            $integration = IntegrationMother::createWithProvider();
            $integrationService = $this->app->get(IntegrationService::class);
            $redirectUrl = IntegrationResource\Pages\EditIntegration::getUrl([$integration]);
            $exceptAuthorizationUrl = $integrationService->generateAuthorizationUrl($integration, $redirectUrl);

            // and not authorized yet
            expect($integration->isAuthorized())->toBeFalse();

            // Act
            $sut = livewire(EditIntegration::class, ['record' => $integration->id])
                ->callAction('authorize');

            // Assert
            $sut->assertRedirect($exceptAuthorizationUrl);
            expect($integration->isAuthorized())->toBeFalse();
        });

        it('exchange authorization code', function () {
            // Arrange
            $integration = IntegrationMother::createWithProvider();

            // and not authorized yet
            expect($integration->isAuthorized())->toBeFalse();

            // Simulate the callback by directly accessing the AuthorizeIntegration page with code parameter
            $authorizeUrl = IntegrationResource\Pages\EditIntegration::getUrl(['record' => $integration->id]);
            $this->get($authorizeUrl . '?code=test_authorization_code');

            // Verify integration is now authorized
            expect($integration->isAuthorized())->toBeTrue();

            // Verify authorization data is stored correctly
            $authorization = $integration->latestAuthorizations();
            expect($integration->isAuthorized())->toBeTrue();
            expect($authorization->authorization)->toHaveKey('access_token');
            expect($authorization->authorization)->toHaveKey('refresh_token');
        });

        it('refreshes token for already authorized integration', function () {
            // Arrange
            $integration = IntegrationMother::createWithProvider();

            // Pre-authorize the integration
            $integration->authorize([
                'access_token' => 'old_access_token',
                'refresh_token' => 'old_refresh_token',
                'expires_in' => 3600,
                'token_type' => 'bearer'
            ]);

            // Verify integration is authorized
            expect($integration->isAuthorized())->toBeTrue();

            // Act - Call the authorize action which should refresh the token
            livewire(EditIntegration::class, ['record' => $integration->id])
                ->callAction('authorize');

            // Refresh the integration from database
            $integration->refresh();

            // Verify authorization data is updated
            $authorization = $integration->latestAuthorizations();
            expect($authorization->authorization['access_token'])->toBe('refreshed_access_token');
            expect($authorization->authorization['refresh_token'])->toBe('refreshed_refresh_token');
        });
    });
});
