<?php

declare(strict_types=1);

use Integration\Domain\Provider;
use Integration\UI\Filament\IntegrationCluster\Resources\ProviderResource;

use Integration\UI\Filament\IntegrationCluster\Resources\ProviderResource\Pages\ListProviders;

use function Pest\Livewire\livewire;

describe('Providers list', function () {
    it('can visit list page', function () {
        livewire(ListProviders::class)->assertSuccessful();
    });

    it('can render list component with providers', function () {
        $integrations = Provider::factory()->count(5)->create();
        livewire(ListProviders::class)->assertCanSeeTableRecords($integrations);
    });
});

describe('Providers Edit', function () {
    it('can edit page', function () {
        $integration = Provider::factory()->make();

        $this->get(ProviderResource::getUrl('create'), [
            'record' => $integration
        ])->assertSuccessful();
    });
});
