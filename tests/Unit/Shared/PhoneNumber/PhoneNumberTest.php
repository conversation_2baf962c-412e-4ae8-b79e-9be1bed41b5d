<?php

declare(strict_types=1);

namespace Tests\Unit\Shared\PhoneNumber;

use InvalidArgumentException;
use Shared\PhoneNumber\PhoneNumber;

describe('PhoneNumber value object', function () {
    it('creates a phone number with valid number and prefix', function () {
        $phoneNumber = new PhoneNumber('123456789', '48');
        expect($phoneNumber->getNumber())->toBe('123456789');
        expect($phoneNumber->getPrefix())->toBe('48');
        expect($phoneNumber->getValue())->toBe('+48123456789');
    });

    it('creates a phone number without prefix', function () {
        $phoneNumber = new PhoneNumber('123456789');
        expect($phoneNumber->getNumber())->toBe('123456789');
        expect($phoneNumber->getPrefix())->toBeNull();
        expect($phoneNumber->getValue())->toBe('123456789');
    });

    it('throws an exception when creating a phone number with a + prefix', function () {
        expect(fn() => new PhoneNumber('+123456789'))->toThrow(InvalidArgumentException::class);
    });

    it('throws an exception when creating a phone number with non-digit characters', function () {
        expect(fn() => new PhoneNumber('123-456-789'))->toThrow(InvalidArgumentException::class);
    });

    it('creates a phone number from E.164 format', function () {
        $phoneNumber = PhoneNumber::fromE164('+48123456789');
        expect($phoneNumber->getNumber())->toBe('123456789');
        expect($phoneNumber->getPrefix())->toBe('48');
        expect($phoneNumber->getValue())->toBe('+48123456789');
    });

    it('throws an exception when creating a phone number from invalid E.164 format', function () {
        expect(fn() => PhoneNumber::fromE164('123456789'))->toThrow(InvalidArgumentException::class);
        expect(fn() => PhoneNumber::fromE164('+0123456789'))->toThrow(InvalidArgumentException::class);
    });

    it('creates a phone number from a string', function () {
        $phoneNumber = PhoneNumber::fromString('123-456-789');
        expect($phoneNumber->getNumber())->toBe('123456789');
        expect($phoneNumber->getPrefix())->toBeNull();
        expect($phoneNumber->getValue())->toBe('123456789');
    });

    it('creates a phone number from a string with a default prefix', function () {
        $phoneNumber = PhoneNumber::fromString('123-456-789', '48');
        expect($phoneNumber->getNumber())->toBe('123456789');
        expect($phoneNumber->getPrefix())->toBe('48');
        expect($phoneNumber->getValue())->toBe('+48123456789');
    });

    it('creates a phone number from a string with a + prefix', function () {
        $phoneNumber = PhoneNumber::fromString('+48123456789');
        expect($phoneNumber->getNumber())->toBe('123456789');
        expect($phoneNumber->getPrefix())->toBe('48');
        expect($phoneNumber->getValue())->toBe('+48123456789');
    });

    it('returns null for empty phone number', function () {
        $phoneNumber = PhoneNumber::fromString(null);
        expect($phoneNumber)->toBeNull();

        $phoneNumber = PhoneNumber::fromString('');
        expect($phoneNumber)->toBeNull();
    });

    it('formats phone number for display', function () {
        // Without prefix
        $phoneNumber = new PhoneNumber('123456789');
        expect($phoneNumber->formatForDisplay())->toBe('123 456 789');

        // With prefix
        $phoneNumber = new PhoneNumber('123456789', '48');
        expect($phoneNumber->formatForDisplay())->toBe('+48 123 456 789');

        // 10-digit number without prefix
        $phoneNumber = new PhoneNumber('1234567890');
        expect($phoneNumber->formatForDisplay())->toBe('************');

        // 10-digit number with prefix
        $phoneNumber = new PhoneNumber('1234567890', '1');
        expect($phoneNumber->formatForDisplay())->toBe('****** 456 7890');
    });

    it('converts to string correctly', function () {
        $phoneNumber = new PhoneNumber('123456789', '48');
        expect((string)$phoneNumber)->toBe('+48123456789');

        $phoneNumber = new PhoneNumber('123456789');
        expect((string)$phoneNumber)->toBe('123456789');
    });

    it('gets common prefixes', function () {
        $prefixes = PhoneNumber::getCommonPrefixes();
        expect($prefixes)->toBeArray();
        expect($prefixes)->toHaveKey('48');
        expect($prefixes)->toHaveKey('1');
        expect($prefixes)->toHaveKey('44');
    });
});
