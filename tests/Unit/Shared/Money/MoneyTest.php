<?php

declare(strict_types=1);

namespace Tests\Unit\Shared\Money;

use InvalidArgumentException;
use Shared\Money\Money;
use Shared\Money\MoneyCalculator;

describe('Money', function () {
    it('uses MoneyCalculator static methods for calculations', function () {
        // This test verifies that Money class uses MoneyCalculator static methods
        // by checking that operations produce the expected results
        $money = new Money('100.50');
        $money2 = new Money('50.25');

        // Test add operation
        $result = $money->add($money2);
        expect($result->getAmount())->toBe(MoneyCalculator::add('100.50', '50.25'));

        // Test subtract operation
        $result = $money->subtract($money2);
        expect($result->getAmount())->toBe(MoneyCalculator::subtract('100.50', '50.25'));

        // Test multiply operation
        $result = $money->multiply('2');
        expect($result->getAmount())->toBe(MoneyCalculator::multiply('100.50', '2'));

        // Test divide operation
        $result = $money->divide('2');
        expect($result->getAmount())->toBe(MoneyCalculator::divide('100.50', '2'));
    });

    it('uses MoneyCalculator for calculations', function () {
        $money = new Money('100.50');
        $reflection = new \ReflectionClass($money);
        $calculatorProperty = $reflection->getProperty('calculator');
        $calculatorProperty->setAccessible(true);

        expect($calculatorProperty->getValue($money))->toBeInstanceOf(MoneyCalculator::class);
    });

    it('creates a money object with a valid amount', function () {
        $money = new Money('100.50');
        expect($money->getAmount())->toBe('100.50');
    });

    it('creates a money object with an integer amount', function () {
        $money = new Money(100);
        expect($money->getAmount())->toBe('100.00');
    });

    it('creates a money object with a float amount', function () {
        $money = new Money(100.50);
        expect($money->getAmount())->toBe('100.50');
    });

    it('throws an exception when creating a money object with a negative amount', function () {
        expect(fn() => new Money('-100.50'))->toThrow(InvalidArgumentException::class);
        expect(fn() => new Money(-100.50))->toThrow(InvalidArgumentException::class);
    });

    it('formats money correctly', function () {
        $money = new Money('1234.56');
        expect($money->format())->toBe('1,234.56');
    });

    it('adds money correctly', function () {
        $money1 = new Money('100.50');
        $money2 = new Money('200.75');
        $result = $money1->add($money2);
        expect($result->getAmount())->toBe('301.25');
    });

    it('subtracts money correctly', function () {
        $money1 = new Money('200.75');
        $money2 = new Money('100.50');
        $result = $money1->subtract($money2);
        expect($result->getAmount())->toBe('100.25');
    });

    it('throws an exception when the result of subtraction would be negative', function () {
        $money1 = new Money('50.25');
        $money2 = new Money('100.50');
        expect(fn() => $money1->subtract($money2))->toThrow(InvalidArgumentException::class);
    });

    it('multiplies money correctly', function () {
        $money = new Money('100.50');
        $result = $money->multiply('2.5');
        expect($result->getAmount())->toBe('251.25');

        // Test with integer
        $result = $money->multiply(2);
        expect($result->getAmount())->toBe('201.00');

        // Test with float
        $result = $money->multiply(1.5);
        expect($result->getAmount())->toBe('150.75');
    });

    it('throws an exception when multiplying by a negative factor', function () {
        $money = new Money('100.50');
        expect(fn() => $money->multiply('-2.5'))->toThrow(InvalidArgumentException::class);
        expect(fn() => $money->multiply(-2.5))->toThrow(InvalidArgumentException::class);
    });

    it('divides money correctly', function () {
        $money = new Money('100.50');
        $result = $money->divide('2');
        expect($result->getAmount())->toBe('50.25');

        // Test with integer
        $result = $money->divide(4);
        expect($result->getAmount())->toBe('25.13');

        // Test with float
        $result = $money->divide(2.5);
        expect($result->getAmount())->toBe('40.20');
    });

    it('throws an exception when dividing by zero', function () {
        $money = new Money('100.50');
        expect(fn() => $money->divide('0'))->toThrow(InvalidArgumentException::class);
        expect(fn() => $money->divide(0))->toThrow(InvalidArgumentException::class);
    });

    it('throws an exception when dividing by a negative divisor', function () {
        $money = new Money('100.50');
        expect(fn() => $money->divide('-2'))->toThrow(InvalidArgumentException::class);
        expect(fn() => $money->divide(-2))->toThrow(InvalidArgumentException::class);
    });

    it('compares money correctly', function () {
        $money1 = new Money('100.50');
        $money2 = new Money('100.50');
        $money3 = new Money('200.75');

        expect($money1->equals($money2))->toBeTrue();
        expect($money1->equals($money3))->toBeFalse();
    });

    it('converts to string correctly', function () {
        $money = new Money('100.50');
        expect((string)$money)->toBe('100.50');
    });

    it('serializes to JSON correctly', function () {
        $money = new Money('100.50');
        $json = json_encode($money);

        expect($json)->toBe('"100.50"');
    });

    it('handles precision correctly', function () {
        $money1 = new Money('0.1');
        $money2 = new Money('0.2');
        $result = $money1->add($money2);

        // This would fail with floating-point arithmetic (0.1 + 0.2 = 0.30000000000000004)
        // But with bcmath it should be exactly 0.30
        expect($result->getAmount())->toBe('0.30');
    });

    it('creates a money object from raw value', function () {
        $money = Money::fromRaw('100.50');
        expect($money->getAmount())->toBe('100.50');

        $money = Money::fromRaw(100.50);
        expect($money->getAmount())->toBe('100.50');

        $money = Money::fromRaw(100);
        expect($money->getAmount())->toBe('100.00');

        $nullMoney = Money::fromRaw(null);
        expect($nullMoney)->toBeNull();
    });
});
