<?php

declare(strict_types=1);

namespace Tests\Unit\Shared\Money;

use Shared\Money\MoneyCalculator;

describe('MoneyCalculator', function () {
    it('adds numbers correctly', function () {
        expect(MoneyCalculator::add('100.50', '200.75'))->toBe('301.25');
        expect(MoneyCalculator::add('0.1', '0.2'))->toBe('0.30');
        expect(MoneyCalculator::add('1000', '500'))->toBe('1500.00');
    });

    it('subtracts numbers correctly', function () {
        expect(MoneyCalculator::subtract('200.75', '100.50'))->toBe('100.25');
        expect(MoneyCalculator::subtract('1000', '500'))->toBe('500.00');
        expect(MoneyCalculator::subtract('10.00', '10.00'))->toBe('0.00');
    });

    it('multiplies numbers correctly', function () {
        expect(MoneyCalculator::multiply('100.50', '2'))->toBe('201.00');
        expect(MoneyCalculator::multiply('100.50', '2.5'))->toBe('251.25');
        expect(MoneyCalculator::multiply('0.1', '0.1'))->toBe('0.01');
    });

    it('divides numbers correctly', function () {
        expect(MoneyCalculator::divide('100.50', '2'))->toBe('50.25');
        expect(MoneyCalculator::divide('100.00', '4'))->toBe('25.00');
        expect(MoneyCalculator::divide('100.00', '3'))->toBe('33.33');
        expect(MoneyCalculator::divide('100.555', '3', 3))->toBe('33.518');
        expect(MoneyCalculator::divide('100.555', '3'))->toBe('33.52');
    });

    it('compares numbers correctly', function () {
        expect(MoneyCalculator::compare('100.50', '100.50'))->toBe(0);
        expect(MoneyCalculator::compare('200.75', '100.50'))->toBe(1);
        expect(MoneyCalculator::compare('100.50', '200.75'))->toBe(-1);
    });

    it('normalizes amounts correctly', function () {
        expect(MoneyCalculator::normalizeAmount('100'))->toBe('100.00');
        expect(MoneyCalculator::normalizeAmount('100.5'))->toBe('100.50');
        expect(MoneyCalculator::normalizeAmount('100.55'))->toBe('100.55');
        expect(MoneyCalculator::normalizeAmount('100.555'))->toBe('100.55');
    });

    it('validates amounts correctly', function () {
        expect(MoneyCalculator::isValidAmount('100.50'))->toBeTrue();
        expect(MoneyCalculator::isValidAmount('0'))->toBeTrue();
        expect(MoneyCalculator::isValidAmount('0.00'))->toBeTrue();

        expect(MoneyCalculator::isValidAmount('-100.50'))->toBeFalse();
        expect(MoneyCalculator::isValidAmount('abc'))->toBeFalse();
    });
});
