<?php

declare(strict_types=1);

namespace Tests\Unit\Shared\Money;

use InvalidArgumentException;
use Shared\Money\Currency;

describe('Currency', function () {
    it('creates a currency object with a valid code', function () {
        $currency = new Currency('PLN');
        expect($currency->getCode())->toBe('PLN');
    });

    it('normalizes currency code to uppercase', function () {
        $currency = new Currency('pln');
        expect($currency->getCode())->toBe('PLN');
    });

    it('throws an exception when creating a currency with an invalid code', function () {
        expect(fn() => new Currency('INVALID'))->toThrow(InvalidArgumentException::class);
    });

    it('returns the correct symbol for each currency', function () {
        $pln = new Currency('PLN');
        expect($pln->getSymbol())->toBe('zł');

        $usd = new Currency('USD');
        expect($usd->getSymbol())->toBe('$');

        $eur = new Currency('EUR');
        expect($eur->getSymbol())->toBe('€');

        $gbp = new Currency('GBP');
        expect($gbp->getSymbol())->toBe('£');
    });

    it('compares currencies correctly', function () {
        $pln1 = new Currency('PLN');
        $pln2 = new Currency('PLN');
        $usd = new Currency('USD');

        expect($pln1->equals($pln2))->toBeTrue();
        expect($pln1->equals($usd))->toBeFalse();
    });

    it('converts to string correctly', function () {
        $currency = new Currency('PLN');
        expect((string)$currency)->toBe('PLN');
    });

    it('serializes to JSON correctly', function () {
        $currency = new Currency('PLN');
        $json = json_encode($currency);

        expect($json)->toBe('"PLN"');
    });

    it('validates currency codes correctly', function () {
        expect(Currency::isValid('PLN'))->toBeTrue();
        expect(Currency::isValid('USD'))->toBeTrue();
        expect(Currency::isValid('EUR'))->toBeTrue();
        expect(Currency::isValid('GBP'))->toBeTrue();
        expect(Currency::isValid('INVALID'))->toBeFalse();
    });
});
