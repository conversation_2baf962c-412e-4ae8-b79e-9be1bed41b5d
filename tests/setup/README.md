# Setup Tests

This directory contains tests that verify the complete environment setup works correctly in a clean Docker-in-Docker container.

## Test Scripts

### Main Setup Test (`test_setup.sh`)

A comprehensive test that verifies the essential functionality using Docker-in-Docker:

- ✅ Taskfile installation
- ✅ Docker installation  
- ✅ Application containers running
- ✅ PHP Artisan working
- ✅ Health endpoint responding

### Sandbox Image Test (`test_sandbox.sh`)

Tests the sandbox stage functionality with multi-language support:

- ✅ Docker-in-Docker functionality
- ✅ Python 3 with pip, pipenv, uv, poetry
- ✅ Node.js with npm, yarn, corepack
- ✅ User environment and permissions
- ✅ Package installation capabilities

**Usage:**
```bash
# Run main setup test from project root
./tests/setup/test_setup.sh

# Run sandbox test from project root
./tests/setup/test_sandbox.sh

# With debug output
./tests/setup/test_setup.sh --debug
./tests/setup/test_sandbox.sh --debug

# Show help
./tests/setup/test_setup.sh --help
./tests/setup/test_sandbox.sh --help
```

### Test Framework

The setup tests use the common `BashTestCase.sh` framework which provides:

- **Standardized logging**: Consistent output formatting with colors
- **Cleanup management**: Automatic cleanup of test resources
- **Utility functions**: Common Docker operations, file checks, etc.
- **Project root detection**: Automatic detection of project directory
- **Test suite management**: Structured test execution and reporting

## File Organization

```
tests/
├── BashTestCase.sh           # Common test framework
├── setup/
│   ├── README.md            # This documentation
│   ├── test_setup.sh        # Main setup test
│   └── test_setup_original.sh # Original test (reference)
└── taskfile/
    └── release/
        ├── test_release.sh
        ├── test_release_simple.sh
        └── validate_environment.sh
```

### Framework Features

The `BashTestCase.sh` framework provides these key features:

1. **Automatic Project Root Detection**: Tests can be run from anywhere
2. **Color-coded Output**: Easy to read test results
3. **Cleanup Management**: Automatic cleanup of Docker containers and resources
4. **Utility Functions**: Common operations like waiting for conditions
5. **Test Case Structure**: Organized test execution with clear reporting
6. **Debug Support**: Enhanced debugging output when `--debug` flag is used

## How It Works

1. **Clean Environment**: Starts with a fresh Docker-in-Docker (DinD) container
2. **Minimal Setup**: Installs only basic dependencies (bash, curl, sudo, git, shadow)
3. **User Creation**: Creates a test user with sudo privileges
4. **Project Copy**: Copies the entire project into the container
5. **Setup Execution**: Runs `setup.sh` as the test user
6. **Verification**: Tests all components are working correctly
7. **Cleanup**: Automatically removes test containers on exit

## Test Environment

- **Base Image**: `docker:dind` (Alpine-based Docker-in-Docker)
- **Docker**: Uses proper Docker-in-Docker without host socket mounting
- **User**: Creates `testuser` with sudo privileges
- **Project Location**: `/home/<USER>/salesto`
- **Network**: All tests run inside the container (no external port mapping needed)

## What Gets Tested

### 1. Taskfile Installation
- Verifies `task` command is available
- Tests basic task listing functionality

### 2. Docker Installation
- Checks Docker CLI is working
- Verifies Docker Compose is available
- Tests container management

### 3. Application Setup
- Confirms PHP container is running
- Verifies PostgreSQL container is running
- Tests container communication

### 4. Laravel Application
- Tests PHP Artisan commands work
- Verifies Laravel version detection
- Checks basic application functionality

### 5. Health Endpoint
- Tests `/up` endpoint responds correctly
- Verifies HTTP server is working
- Confirms application is accessible

## Debugging Failed Tests

If tests fail, you can debug by:

1. **Connect to Container** (while test is running):
   ```bash
   # Find the container name
   docker ps | grep salesto-test
   
   # Connect to it
   docker exec -it salesto-test-test-TIMESTAMP bash
   ```

2. **Check Logs**:
   ```bash
   # Inside container
   cd /home/<USER>/salesto
   docker compose logs
   ```

3. **Manual Testing**:
   ```bash
   # Test components manually
   task --version
   docker --version
   docker compose ps
   curl http://localhost/up
   ```

4. **View Test Output**: The test script provides detailed debug information when failures occur

## Requirements

- Docker installed on host machine
- Sufficient disk space for Docker DinD image and containers
- Internet connection for package downloads
- Docker daemon running

## Troubleshooting

### Common Issues

1. **Docker Socket Permission**:
   - Ensure your user is in the `docker` group
   - Or run with `sudo` if necessary

2. **Container Cleanup Issues**:
   ```bash
   # Manual cleanup
   docker stop $(docker ps -a -q --filter name=salesto-test)
   docker rm $(docker ps -a -q --filter name=salesto-test)
   ```

3. **Network Issues**:
   - Check internet connectivity
   - Verify Docker daemon is running
   - Check firewall settings

4. **DinD Issues**:
   - Ensure `--privileged` flag is supported
   - Check Docker version compatibility

### Debug Information

The tests provide debug information when failures occur:
- Container logs
- Process lists
- Network status
- File permissions

## Integration with CI/CD

The test can be integrated into CI/CD pipelines:

```yaml
# Example GitHub Actions step
- name: Test Setup Script
  run: |
    chmod +x tests/setup/test_setup.sh
    ./tests/setup/test_setup.sh
```

## Performance

- **Test Duration**: ~5-10 minutes
- **Time Factors**: 
  - Docker DinD image download (first run)
  - Alpine package installation
  - Application startup
  - Service readiness checks
  - Health endpoint verification (up to 15 attempts with 3s intervals)
