#!/bin/bash

# test_setup.sh - Test setup.sh using Docker-in-Docker
# Uses a proper DinD image without mounting host Docker socket

set -e

# Load test framework
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "${SCRIPT_DIR}/../BashTestCase.sh"

# =============================================================================
# TEST CONFIGURATION
# =============================================================================

CONTAINER_NAME="salesto-$(generate_test_id)"
TEST_USER="testuser"
TEST_HOME="/home/<USER>"
PROJECT_IN_CONTAINER="${TEST_HOME}/salesto"

# =============================================================================
# DOCKER UTILITIES
# =============================================================================

# Execute command in container as test user
exec_in_container() {
    docker exec -w "$PROJECT_IN_CONTAINER" -u "$TEST_USER" "$CONTAINER_NAME" bash -c "$1"
}

# Execute command in container as root
exec_in_container_root() {
    docker exec "$CONTAINER_NAME" sh -c "$1"
}

# =============================================================================
# CLEANUP FUNCTIONS
# =============================================================================

cleanup_test_container() {
    docker_cleanup_container "$CONTAINER_NAME"
}

# =============================================================================
# TEST SETUP FUNCTIONS
# =============================================================================

setup_test_container() {
    print_status "INFO" "Starting Docker-in-Docker container..."

    # Check if setup.sh exists
    check_project_file "setup.sh"

    # Start container
    docker run -dt --privileged \
        --name "$CONTAINER_NAME" \
        -v "$PROJECT_ROOT:/workspace" \
        docker:dind

    print_status "PASS" "Container started: $CONTAINER_NAME"
}

install_container_dependencies() {
    print_status "INFO" "Installing basic tools in container..."

    exec_in_container_root "
        apk update
        apk add --no-cache bash curl sudo git shadow

        # Create test user
        adduser -D -s /bin/bash $TEST_USER
        echo '$TEST_USER:' | chpasswd -e

        # Add testuser to docker group for Docker access
        addgroup docker 2>/dev/null || true
        adduser $TEST_USER docker

        # Copy project
        cp -r /workspace $PROJECT_IN_CONTAINER
        chown -R $TEST_USER:$TEST_USER $PROJECT_IN_CONTAINER
    "

    print_status "PASS" "Dependencies installed"
}

wait_for_docker_daemon() {
    print_status "INFO" "Waiting for Docker daemon to be ready..."

    wait_for_condition \
        "docker exec '$CONTAINER_NAME' docker info" \
        60 \
        2 \
        "Docker daemon"
}

run_setup_script() {
    print_status "INFO" "Running setup.sh..."

    if docker exec "$CONTAINER_NAME" bash -c "
        cd $PROJECT_IN_CONTAINER
        chmod +x setup.sh
        export HOME=$TEST_HOME
        export USER=$TEST_USER
        cp .env.example .env
        cp .env.devops.example .env.devops
        ./setup.sh

        # Ensure testuser can access installed components
        chown -R $TEST_USER:$TEST_USER $TEST_HOME/

        # Make sure testuser is in docker group (in case it was modified during setup)
        adduser $TEST_USER docker 2>/dev/null || true
    "; then
        print_status "PASS" "Setup script completed successfully"
    else
        print_status "FAIL" "Setup script failed"
    fi
}

# =============================================================================
# TEST CASES
# =============================================================================

test_taskfile_installation() {
    print_status "INFO" "Testing Taskfile installation..."

    # Debug: Check for task binary
    print_status "DEBUG" "Checking for task binary..."
    exec_in_container "task --list-all" || true

    if exec_in_container "export PATH=\$PATH:/usr/local/bin && task --version" >/dev/null 2>&1; then
        local task_version=$(exec_in_container "export PATH=\$PATH:/usr/local/bin && task --version" 2>/dev/null)
        print_status "PASS" "Taskfile is installed: $task_version"
        return 0
    else
        print_status "FAIL" "Taskfile is not working" false
        # Show debug info
        print_status "DEBUG" "Checking setup logs for Taskfile installation..."
        exec_in_container "ls -la setup/" || true
        return 1
    fi
}

test_docker_installation() {
    print_status "INFO" "Testing Docker installation..."

    if docker exec -u "$TEST_USER" "$CONTAINER_NAME" docker --version >/dev/null 2>&1; then
        local docker_version=$(docker exec -u "$TEST_USER" "$CONTAINER_NAME" docker --version 2>/dev/null)
        print_status "PASS" "Docker is working: $docker_version"
        return 0
    else
        print_status "FAIL" "Docker is not working for $TEST_USER" false
        return 1
    fi
}

test_application_containers() {
    print_status "INFO" "Testing application containers..."

    # Show current container status
    docker exec -u "$TEST_USER" "$CONTAINER_NAME" docker ps

    if exec_in_container "docker compose ps --services --filter 'status=running'" | grep -q "php"; then
        print_status "PASS" "PHP container is running"
        return 0
    else
        print_status "WARN" "PHP container may not be running, checking status..."
        exec_in_container "docker compose ps" || true

        # Try to start if not running
        print_status "INFO" "Attempting to start containers..."
        exec_in_container "docker compose up -d" || true
        sleep 15

        if exec_in_container "docker compose ps --services --filter 'status=running'" | grep -q "php"; then
            print_status "PASS" "PHP container is now running"
            return 0
        else
            print_status "FAIL" "PHP container failed to start" false
            return 1
        fi
    fi
}

test_php_artisan() {
    print_status "INFO" "Testing PHP Artisan..."

    if exec_in_container "docker compose exec -T php php artisan --version" >/dev/null 2>&1; then
        local version=$(exec_in_container "docker compose exec -T php php artisan --version" 2>/dev/null | head -1)
        print_status "PASS" "PHP Artisan is working: $version"
        return 0
    else
        print_status "FAIL" "PHP Artisan is not working" false
        # Debug info
        exec_in_container "docker compose logs php --tail=10" || true
        return 1
    fi
}

test_health_endpoint() {
    print_status "INFO" "Testing health endpoint..."

    # Get the APP_PORT from the .env file, default to 80
    print_status "DEBUG" "Reading APP_PORT from .env file..."
    local app_port=$(exec_in_container 'if [ -f .env ]; then grep "^APP_PORT=" .env | cut -d"=" -f2; else echo "80"; fi')
    # Fallback to 80 if empty
    app_port=${app_port:-80}
    print_status "DEBUG" "Detected APP_PORT: '$app_port'"

    local base_url="http://localhost:$app_port"
    local health_url="$base_url/up"

    print_status "INFO" "Using APP_PORT: $app_port"
    print_status "INFO" "Health URL: $health_url"

    # Wait for health endpoint to be ready
    if wait_for_condition \
        "exec_in_container 'curl -f -s $health_url'" \
        45 \
        3 \
        "health endpoint"; then

        local response=$(exec_in_container "curl -s $health_url" 2>/dev/null)
        print_status "PASS" "Health endpoint (/up) is responding: $response"
        return 0
    else
        print_status "FAIL" "Health endpoint is not responding" false

        # Debug info
        print_status "DEBUG" "Checking http container..."
        exec_in_container "docker compose logs http --tail=10" || true

        print_status "DEBUG" "Checking PHP container..."
        exec_in_container "docker compose logs php --tail=10" || true

        print_status "DEBUG" "Testing direct http connection..."
        exec_in_container "curl -v $base_url/" || true

        print_status "DEBUG" "Testing health endpoint directly..."
        exec_in_container "curl -v $health_url" || true

        print_status "DEBUG" "Checking all running containers..."
        exec_in_container "docker compose ps" || true

        return 1
    fi
}

# =============================================================================
# MAIN TEST EXECUTION
# =============================================================================

main() {
    # Initialize test suite
    init_test_suite "Setup Test Suite" "$CONTAINER_NAME"

    # Register cleanup
    register_cleanup cleanup_test_container

    # Show configuration
    log_info "Container name: $CONTAINER_NAME"
    log_info "Test user: $TEST_USER"
    log_info "Project in container: $PROJECT_IN_CONTAINER"

    # Setup phase
    setup_test_container
    install_container_dependencies
    wait_for_docker_daemon
    run_setup_script

    # Test phase
    local failed_tests=0

    run_test_case "Taskfile Installation" test_taskfile_installation || ((failed_tests++))
    run_test_case "Docker Installation" test_docker_installation || ((failed_tests++))
    run_test_case "Application Containers" test_application_containers || ((failed_tests++))
    run_test_case "PHP Artisan" test_php_artisan || ((failed_tests++))
    run_test_case "Health Endpoint" test_health_endpoint || ((failed_tests++))

    # Complete test suite
    if [[ $failed_tests -eq 0 ]]; then
        complete_test_suite 0
    else
        log_error "$failed_tests test(s) failed"
        complete_test_suite 1
    fi
}

# =============================================================================
# SCRIPT ENTRY POINT
# =============================================================================

# Show usage
if [[ "${1:-}" == "-h" || "${1:-}" == "--help" ]]; then
    echo "Usage: $0 [--debug]"
    echo "Test setup.sh in Docker-in-Docker container"
    echo "Tests: Taskfile, Docker, Application containers, PHP Artisan, Health endpoint"
    echo "All tests run inside the container - no external network access needed"
    echo ""
    echo "Options:"
    echo "  --debug    Enable debug output"
    echo "  -h, --help Show this help message"
    exit 0
fi

# Enable debug mode if requested
if [[ "${1:-}" == "--debug" ]]; then
    export DEBUG=true
    print_status "DEBUG" "Debug mode enabled"
fi

# Run main function
main "$@"
