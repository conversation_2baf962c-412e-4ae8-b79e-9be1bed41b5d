#!/bin/bash

# test_sandbox.sh - Test sandbox image functionality
# Tests the sandbox stage with Docker-in-Docker, Python, and Node.js

set -e

# Load test framework
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "${SCRIPT_DIR}/../BashTestCase.sh"

# =============================================================================
# TEST CONFIGURATION
# =============================================================================

CONTAINER_NAME="salesto-sandbox-$(generate_test_id)"
TEST_USER="allhands"
TEST_HOME="/home/<USER>"
PROJECT_IN_CONTAINER="/workspace"
SANDBOX_IMAGE="salesto:sandbox"

# =============================================================================
# DOCKER UTILITIES
# =============================================================================

# Execute command in container as test user
exec_in_container() {
    docker exec -w "$PROJECT_IN_CONTAINER" -u "$TEST_USER" "$CONTAINER_NAME" bash -c "$1"
}

# Execute command in container as root
exec_in_container_root() {
    docker exec "$CONTAINER_NAME" sh -c "$1"
}

# Execute Docker Compose command with retry logic to handle race conditions
exec_docker_compose_with_retry() {
    local cmd="$1"
    local max_retries=3
    local retry_delay=5
    local attempt=1
    
    while [[ $attempt -le $max_retries ]]; do
        print_status "INFO" "Attempting Docker Compose command (attempt $attempt/$max_retries): $cmd"
        
        # Add a small random delay to reduce chance of simultaneous operations
        sleep $((RANDOM % 3 + 1))
        
        local output
        if output=$(exec_in_container "$cmd" 2>&1); then
            return 0
        else
            local exit_code=$?
            
            # Check if it's the specific concurrent map writes error
            if echo "$output" | grep -q "concurrent map writes"; then
                print_status "WARN" "Docker Compose race condition detected on attempt $attempt"
            else
                print_status "WARN" "Docker Compose command failed on attempt $attempt: $output"
            fi
            
            if [[ $attempt -lt $max_retries ]]; then
                print_status "INFO" "Waiting ${retry_delay}s before retry..."
                sleep $retry_delay
                
                # Only clean up if it was a race condition
                if echo "$output" | grep -q "concurrent map writes"; then
                    print_status "INFO" "Cleaning up after race condition..."
                    exec_in_container "docker compose down --remove-orphans" >/dev/null 2>&1 || true
                    sleep 3
                fi
            else
                print_status "FAIL" "Final error output: $output"
            fi
        fi
        
        ((attempt++))
    done
    
    print_status "FAIL" "Docker Compose command failed after $max_retries attempts"
    return 1
}

# =============================================================================
# CLEANUP FUNCTIONS
# =============================================================================

cleanup_test_container() {
    docker_cleanup_container "$CONTAINER_NAME"
}

cleanup_existing_containers() {
    print_status "INFO" "Checking for Docker Compose lock files that might cause race conditions..."
    
    # Check if there are any Docker Compose operations in progress
    if docker ps --filter "label=com.docker.compose.project" --format "{{.Names}}" | grep -q "salesto"; then
        print_status "WARN" "Found running salesto containers - this might cause race conditions"
        print_status "INFO" "Consider stopping them with: docker compose down"
    fi
    
    # Just wait a moment to let any ongoing operations complete
    sleep 2
    print_status "PASS" "Pre-flight check completed"
}

# =============================================================================
# TEST SETUP FUNCTIONS
# =============================================================================

build_sandbox_image() {
    print_status "INFO" "Building sandbox image..."

    # Check if Dockerfile exists
    check_project_file "Dockerfile"

    # Verify that setup.sh exists in the build context
    check_project_file "setup.sh"

    print_status "INFO" "Build context: $PROJECT_ROOT"
    print_status "INFO" "Files in build context:"
    ls -la "$PROJECT_ROOT/setup.sh" "$PROJECT_ROOT/setup/" 2>/dev/null || true

    # Build the sandbox stage
    if docker build --target sandbox -t "$SANDBOX_IMAGE" "$PROJECT_ROOT"; then
        print_status "PASS" "Sandbox image built successfully"

        # Verify setup.sh was copied into the image
        print_status "INFO" "Verifying setup.sh in built image..."
        if docker run --rm --privileged --entrypoint="" "$SANDBOX_IMAGE" ls -la /workspace/setup.sh >/dev/null 2>&1; then
            print_status "PASS" "setup.sh found in built image"
        else
            print_status "WARN" "setup.sh not found in built image"
        fi
    else
        print_status "FAIL" "Failed to build sandbox image"
        return 1
    fi
}

setup_test_container() {
    print_status "INFO" "Starting sandbox container..."

    # Start container from our sandbox image
    # Mount the project root to /workspace (this will mask the setup.sh copied during build)
    docker run -dt --privileged \
        --name "$CONTAINER_NAME" \
        -v "$PROJECT_ROOT:/workspace" \
        "$SANDBOX_IMAGE"

    print_status "PASS" "Sandbox container started: $CONTAINER_NAME"

    # Verify that setup.sh is available in the mounted workspace
    print_status "INFO" "Verifying setup.sh is available in container..."
    if docker exec "$CONTAINER_NAME" ls -la /workspace/setup.sh >/dev/null 2>&1; then
        print_status "PASS" "setup.sh found in mounted workspace"
    else
        print_status "FAIL" "setup.sh not found in mounted workspace"
        print_status "INFO" "Container workspace contents:"
        docker exec "$CONTAINER_NAME" ls -la /workspace/ | head -10
        return 1
    fi
}

wait_for_container_ready() {
    print_status "INFO" "Waiting for container services to be ready..."

    # Wait for Docker daemon, Python, and Node.js to be ready
    wait_for_condition \
        "docker exec '$CONTAINER_NAME' docker info && docker exec '$CONTAINER_NAME' python3 --version && docker exec '$CONTAINER_NAME' node --version" \
        120 \
        3 \
        "container services"
}

# =============================================================================
# TEST CASES
# =============================================================================

test_docker_functionality() {
    print_status "INFO" "Testing Docker functionality..."

    # Test Docker daemon
    if docker exec "$CONTAINER_NAME" docker info >/dev/null 2>&1; then
        local docker_version=$(docker exec "$CONTAINER_NAME" docker --version 2>/dev/null)
        print_status "PASS" "Docker daemon is working: $docker_version"
    else
        print_status "FAIL" "Docker daemon is not working" false
        return 1
    fi

    # Test Docker Compose
    if docker exec "$CONTAINER_NAME" docker compose version >/dev/null 2>&1; then
        local compose_version=$(docker exec "$CONTAINER_NAME" docker compose version 2>/dev/null | head -1)
        print_status "PASS" "Docker Compose is working: $compose_version"
    else
        print_status "FAIL" "Docker Compose is not working" false
        return 1
    fi

    # Test user can access Docker
    if docker exec -u "$TEST_USER" "$CONTAINER_NAME" docker ps >/dev/null 2>&1; then
        print_status "PASS" "User $TEST_USER can access Docker"
        return 0
    else
        print_status "FAIL" "User $TEST_USER cannot access Docker" false
        return 1
    fi
}

test_python_functionality() {
    print_status "INFO" "Testing Python functionality..."

    # Test Python 3
    if docker exec "$CONTAINER_NAME" python3 --version >/dev/null 2>&1; then
        local python_version=$(docker exec "$CONTAINER_NAME" python3 --version 2>/dev/null)
        print_status "PASS" "Python is working: $python_version"
    else
        print_status "FAIL" "Python is not working" false
        return 1
    fi

    # Test pip
    if docker exec "$CONTAINER_NAME" pip3 --version >/dev/null 2>&1; then
        local pip_version=$(docker exec "$CONTAINER_NAME" pip3 --version 2>/dev/null)
        print_status "PASS" "pip is working: $pip_version"
    else
        print_status "FAIL" "pip is not working" false
        return 1
    fi

    # Test pipenv
    if docker exec "$CONTAINER_NAME" pipenv --version >/dev/null 2>&1; then
        local pipenv_version=$(docker exec "$CONTAINER_NAME" pipenv --version 2>/dev/null)
        print_status "PASS" "pipenv is working: $pipenv_version"
    else
        print_status "FAIL" "pipenv is not working" false
        return 1
    fi

    # Test poetry
    if docker exec "$CONTAINER_NAME" poetry --version >/dev/null 2>&1; then
        local poetry_version=$(docker exec "$CONTAINER_NAME" poetry --version 2>/dev/null)
        print_status "PASS" "poetry is working: $poetry_version"
    else
        print_status "FAIL" "poetry is not working" false
        return 1
    fi

    # Test uv
    if docker exec "$CONTAINER_NAME" uv --version >/dev/null 2>&1; then
        local uv_version=$(docker exec "$CONTAINER_NAME" uv --version 2>/dev/null)
        print_status "PASS" "uv is working: $uv_version"
        return 0
    else
        print_status "FAIL" "uv is not working" false
        return 1
    fi
}

test_nodejs_functionality() {
    print_status "INFO" "Testing Node.js functionality..."

    # Test Node.js
    if docker exec "$CONTAINER_NAME" node --version >/dev/null 2>&1; then
        local node_version=$(docker exec "$CONTAINER_NAME" node --version 2>/dev/null)
        print_status "PASS" "Node.js is working: $node_version"
    else
        print_status "FAIL" "Node.js is not working" false
        return 1
    fi

    # Test npm
    if docker exec "$CONTAINER_NAME" npm --version >/dev/null 2>&1; then
        local npm_version=$(docker exec "$CONTAINER_NAME" npm --version 2>/dev/null)
        print_status "PASS" "npm is working: $npm_version"
    else
        print_status "FAIL" "npm is not working" false
        return 1
    fi

    # Test yarn
    if docker exec "$CONTAINER_NAME" yarn --version >/dev/null 2>&1; then
        local yarn_version=$(docker exec "$CONTAINER_NAME" yarn --version 2>/dev/null)
        print_status "PASS" "yarn is working: $yarn_version"
    else
        print_status "FAIL" "yarn is not working" false
        return 1
    fi

    # Test corepack (optional)
    if docker exec "$CONTAINER_NAME" corepack --version >/dev/null 2>&1; then
        local corepack_version=$(docker exec "$CONTAINER_NAME" corepack --version 2>/dev/null)
        print_status "PASS" "corepack is working: $corepack_version"
    else
        print_status "WARN" "corepack is not available (optional)"
    fi

    return 0
}

test_user_environment() {
    print_status "INFO" "Testing user environment..."

    # Test user exists
    if docker exec "$CONTAINER_NAME" id "$TEST_USER" >/dev/null 2>&1; then
        print_status "PASS" "User $TEST_USER exists"
    else
        print_status "FAIL" "User $TEST_USER does not exist" false
        return 1
    fi

    # Test user has sudo access
    if docker exec -u "$TEST_USER" "$CONTAINER_NAME" sudo -n true >/dev/null 2>&1; then
        print_status "PASS" "User $TEST_USER has sudo access"
    else
        print_status "FAIL" "User $TEST_USER does not have sudo access" false
        return 1
    fi

    # Test user can access workspace
    if docker exec -u "$TEST_USER" "$CONTAINER_NAME" ls /workspace >/dev/null 2>&1; then
        print_status "PASS" "User $TEST_USER can access workspace"
    else
        print_status "FAIL" "User $TEST_USER cannot access workspace" false
        return 1
    fi

    # Test user home directory
    if docker exec -u "$TEST_USER" "$CONTAINER_NAME" test -d "$TEST_HOME"; then
        print_status "PASS" "User home directory exists: $TEST_HOME"
        return 0
    else
        print_status "FAIL" "User home directory does not exist: $TEST_HOME" false
        return 1
    fi
}

test_package_installation() {
    print_status "INFO" "Testing package installation capabilities..."

    # Test Python package installation (using virtual environment)
    if docker exec -u "$TEST_USER" "$CONTAINER_NAME" bash -c "cd /tmp && python3 -m venv test_venv && source test_venv/bin/activate && pip install requests" >/dev/null 2>&1; then
        print_status "PASS" "Can install Python packages"
    else
        print_status "FAIL" "Cannot install Python packages" false
        return 1
    fi

    # Test Node.js package installation
    if docker exec -u "$TEST_USER" "$CONTAINER_NAME" sh -c "cd /tmp && npm init -y && npm install lodash" >/dev/null 2>&1; then
        print_status "PASS" "Can install Node.js packages"
    else
        print_status "FAIL" "Cannot install Node.js packages" false
        return 1
    fi

    # Test Docker container operations
    if docker exec -u "$TEST_USER" "$CONTAINER_NAME" docker run --rm hello-world >/dev/null 2>&1; then
        print_status "PASS" "Can run Docker containers"
        return 0
    else
        print_status "FAIL" "Cannot run Docker containers" false
        return 1
    fi
}

test_setup_script() {
    print_status "INFO" "Testing setup script execution..."

    # Check Docker status before running setup
    if ! exec_in_container "docker info" >/dev/null 2>&1; then
        print_status "FAIL" "Docker daemon is not ready" false
        return 1
    fi

    # Debug buildx configuration before running setup
    print_status "INFO" "Checking buildx configuration in container..."
    docker exec -w /workspace "$CONTAINER_NAME" bash -c "docker buildx ls" || true

    # Run the setup script to install Task and other dependencies
    print_status "INFO" "Running setup script..."

    # Create a temporary file to capture the full output
    local setup_output=$(mktemp)

    # Setup environment files and run setup script
    if docker exec -w /workspace "$CONTAINER_NAME" bash -c "
        # Copy example env files to ensure clean environment
        cp .env.example .env
        cp .env.devops.example .env.devops
        ./setup.sh
    "; then
        print_status "PASS" "Setup script executed successfully"
        rm -f "$setup_output"
    else
        print_status "FAIL" "Setup script failed" false
        # Show the error output for debugging
        print_status "INFO" "Setup script full output:"
        cat "$setup_output" | tail -50
        rm -f "$setup_output"
        return 1
    fi

    return 0
}

test_task_installation() {
    print_status "INFO" "Testing Task (taskfile) installation..."

    # Test Task
    if docker exec "$CONTAINER_NAME" task --version >/dev/null 2>&1; then
        local task_version=$(docker exec "$CONTAINER_NAME" task --version 2>/dev/null)
        print_status "PASS" "Task is working: $task_version"
        return 0
    else
        print_status "FAIL" "Task is not working" false
        return 1
    fi
}

test_application_build() {
    print_status "INFO" "Testing application build with docker-compose..."

    # Clean up any existing containers to prevent race conditions
    print_status "INFO" "Cleaning up existing containers to prevent race conditions..."
    exec_in_container "task down --remove-orphans --volumes" >/dev/null 2>&1 || true
    
    # Wait a moment for cleanup to complete
    sleep 5

    # Build the application using Task with retry logic
    print_status "INFO" "Building application containers..."
    if exec_docker_compose_with_retry "task build"; then
        print_status "PASS" "Application containers built successfully"
    else
        print_status "FAIL" "Failed to build application containers" false
        return 1
    fi

    return 0
}

test_application_startup() {
    print_status "INFO" "Testing application startup with docker-compose..."

    # Ensure clean state before starting
    print_status "INFO" "Ensuring clean state before startup..."
    exec_in_container "task down" >/dev/null 2>&1 || true
    sleep 3

    # Start the application using Task with retry logic
    print_status "INFO" "Starting application with task up..."
    if exec_docker_compose_with_retry "task up"; then
        print_status "PASS" "Application started successfully"
    else
        print_status "FAIL" "Failed to start application" false
        return 1
    fi

    # Wait for services to be ready
    print_status "INFO" "Waiting for application services to be ready..."
    sleep 30

    # Check if containers are running
    if exec_in_container "docker compose ps --format json" >/dev/null 2>&1; then
        local running_containers=$(exec_in_container "docker compose ps --format json | jq -r '.State' | grep -c running" 2>/dev/null || echo "0")
        if [[ "$running_containers" -gt 0 ]]; then
            print_status "PASS" "Application containers are running ($running_containers containers)"
        else
            print_status "FAIL" "No application containers are running" false
            return 1
        fi
    else
        print_status "FAIL" "Failed to check container status" false
        return 1
    fi

    return 0
}

test_application_endpoints() {
    print_status "INFO" "Testing application endpoints..."

    # Test health endpoint
    if exec_in_container "curl -f http://localhost:80/up" >/dev/null 2>&1; then
        print_status "PASS" "Health endpoint is responding"
    else
        print_status "FAIL" "Health endpoint is not responding" false
        return 1
    fi

    # Test main application endpoint
    if exec_in_container "curl -f http://localhost:80" >/dev/null 2>&1; then
        print_status "PASS" "Application homepage is accessible"
    else
        print_status "FAIL" "Application homepage is not accessible" false
        return 1
    fi

    return 0
}

test_application_commands() {
    print_status "INFO" "Testing application commands through Task..."

    # Test artisan command through Task
    if exec_in_container "task artisan -- --version" >/dev/null 2>&1; then
        local artisan_version=$(exec_in_container "task artisan -- --version" 2>/dev/null)
        print_status "PASS" "Laravel Artisan is working through Task: $artisan_version"
    else
        print_status "FAIL" "Laravel Artisan is not working through Task" false
        return 1
    fi

    # Test composer command through Task
    if exec_in_container "task composer -- --version" >/dev/null 2>&1; then
        local composer_version=$(exec_in_container "task composer -- --version" 2>/dev/null)
        print_status "PASS" "Composer is working through Task: $composer_version"
    else
        print_status "FAIL" "Composer is not working through Task" false
        return 1
    fi

    return 0
}

test_application_cleanup() {
    print_status "INFO" "Testing application cleanup..."

    # Stop the application using Task
    if exec_in_container "task down" >/dev/null 2>&1; then
        print_status "PASS" "Application stopped successfully"
    else
        print_status "FAIL" "Failed to stop application" false
        return 1
    fi

    return 0
}

# =============================================================================
# MAIN TEST EXECUTION
# =============================================================================

main() {
    # Initialize test suite
    init_test_suite "Sandbox Image Test Suite" "$CONTAINER_NAME"

    # Register cleanup
    register_cleanup cleanup_test_container

    # Show configuration
    log_info "Container name: $CONTAINER_NAME"
    log_info "Test user: $TEST_USER"
    log_info "Sandbox image: $SANDBOX_IMAGE"
    log_info "Project in container: $PROJECT_IN_CONTAINER"

    # Clean up any existing containers to prevent race conditions
    cleanup_existing_containers

    # Setup phase
    build_sandbox_image
    setup_test_container
    wait_for_container_ready

    # Test phase
    local failed_tests=0

    # Basic sandbox functionality tests
    run_test_case "Docker Functionality" test_docker_functionality || ((failed_tests++))
    run_test_case "Python Functionality" test_python_functionality || ((failed_tests++))
    run_test_case "Node.js Functionality" test_nodejs_functionality || ((failed_tests++))
    run_test_case "User Environment" test_user_environment || ((failed_tests++))
    run_test_case "Package Installation" test_package_installation || ((failed_tests++))

    # Application-specific tests
    run_test_case "Setup Script" test_setup_script || ((failed_tests++))
    run_test_case "Task Installation" test_task_installation || ((failed_tests++))
    run_test_case "Application Build" test_application_build || ((failed_tests++))
    run_test_case "Application Startup" test_application_startup || ((failed_tests++))
    run_test_case "Application Endpoints" test_application_endpoints || ((failed_tests++))
    run_test_case "Application Commands" test_application_commands || ((failed_tests++))
    run_test_case "Application Cleanup" test_application_cleanup || ((failed_tests++))

    # Complete test suite
    if [[ $failed_tests -eq 0 ]]; then
        complete_test_suite 0
    else
        log_error "$failed_tests test(s) failed"
        complete_test_suite 1
    fi
}

# =============================================================================
# SCRIPT ENTRY POINT
# =============================================================================

# Show usage
if [[ "${1:-}" == "-h" || "${1:-}" == "--help" ]]; then
    echo "Usage: $0 [--debug]"
    echo "Test sandbox image functionality and application deployment"
    echo ""
    echo "Basic Tests: Docker, Python, Node.js, User environment, Package installation"
    echo "Application Tests: Setup script, Task, Build, Startup, Endpoints, Commands, Cleanup"
    echo ""
    echo "Options:"
    echo "  --debug    Enable debug output"
    echo "  -h, --help Show this help message"
    exit 0
fi

# Enable debug mode if requested
if [[ "${1:-}" == "--debug" ]]; then
    export DEBUG=true
    print_status "DEBUG" "Debug mode enabled"
fi

# Run main function
main "$@"
