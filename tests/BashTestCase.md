# BashTestCase Framework - Best Practices Guide

This guide provides best practices for writing bash tests using the BashTestCase.sh framework in this project.

## Table of Contents

1. [Framework Overview](#framework-overview)
2. [Getting Started](#getting-started)
3. [Core Concepts](#core-concepts)
4. [Best Practices](#best-practices)
5. [Common Patterns](#common-patterns)
6. [Available Functions](#available-functions)
7. [Examples](#examples)
8. [Troubleshooting](#troubleshooting)

## Framework Overview

The BashTestCase.sh framework provides:

- **Standardized logging**: Consistent, color-coded output
- **Automatic cleanup**: Resource management with cleanup handlers
- **Project root detection**: Tests work from any directory
- **Utility functions**: Common operations for Docker, tasks, and conditions
- **Test suite management**: Structured test execution and reporting
- **Debug support**: Enhanced debugging with `--debug` flag

## Getting Started

### Basic Test Structure

```bash
#!/bin/bash

# Source the framework
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/../BashTestCase.sh"

# Initialize test suite
init_test_suite "My Test Suite"

# Define cleanup function
cleanup_my_test() {
    print_status "INFO" "Cleaning up test resources..."
    # Add cleanup logic here
}

# Register cleanup
register_cleanup cleanup_my_test

# Your test logic here
run_test_case "Test Description" test_function_name

# Complete test suite
complete_test_suite $?
```

### Minimal Example

```bash
#!/bin/bash
source "$(dirname "$0")/../BashTestCase.sh"

init_test_suite "Simple Test"

test_basic_functionality() {
    print_status "INFO" "Testing basic functionality..."
    
    if check_command "docker"; then
        print_status "PASS" "Docker is available"
        return 0
    else
        print_status "FAIL" "Docker not found"
        return 1
    fi
}

run_test_case "Basic Functionality" test_basic_functionality
complete_test_suite $?
```

## Core Concepts

### 1. Test Suite Lifecycle

```bash
# 1. Initialize
init_test_suite "Suite Name"

# 2. Register cleanup (optional but recommended)
register_cleanup cleanup_function

# 3. Run test cases
run_test_case "Description" test_function

# 4. Complete
complete_test_suite $exit_code
```

### 2. Logging Levels

```bash
print_status "INFO" "Information message"
print_status "PASS" "Success message"
print_status "FAIL" "Failure message"
print_status "WARN" "Warning message"
print_status "DEBUG" "Debug message"

# Convenience functions
log_info "Information"
log_success "Success"
log_error "Error"
log_warning "Warning"
log_debug "Debug"
```

### 3. Cleanup Management

```bash
cleanup_my_test() {
    # Stop containers
    docker_cleanup_container "my-test-container"
    
    # Remove temporary files
    rm -f /tmp/my-test-file
    
    # Other cleanup
}

register_cleanup cleanup_my_test
```

## Best Practices

### 1. **Always Use the Framework**

❌ **Don't:**
```bash
echo "Starting test..."
if some_command; then
    echo "✓ Test passed"
else
    echo "✗ Test failed"
    exit 1
fi
```

✅ **Do:**
```bash
print_status "INFO" "Starting test..."
if some_command; then
    print_status "PASS" "Test passed"
else
    print_status "FAIL" "Test failed"
    return 1
fi
```

### 2. **Use Test Cases for Organization**

❌ **Don't:**
```bash
# All logic in main script
print_status "INFO" "Testing feature A..."
# ... test logic ...
print_status "INFO" "Testing feature B..."
# ... test logic ...
```

✅ **Do:**
```bash
test_feature_a() {
    print_status "INFO" "Testing feature A..."
    # ... test logic ...
    return $?
}

test_feature_b() {
    print_status "INFO" "Testing feature B..."
    # ... test logic ...
    return $?
}

run_test_case "Feature A" test_feature_a
run_test_case "Feature B" test_feature_b
```

### 3. **Always Register Cleanup**

❌ **Don't:**
```bash
# Start resources without cleanup
docker run -d --name test-container nginx
# ... test logic ...
# Resources left running if test fails
```

✅ **Do:**
```bash
cleanup_test() {
    docker_cleanup_container "test-container"
}

register_cleanup cleanup_test
docker run -d --name test-container nginx
```

### 4. **Use Framework Utilities**

❌ **Don't:**
```bash
# Manual task execution
if task -f taskfile.release.yaml build:dev CLI_ARGS="v1.0"; then
    echo "Build successful"
else
    echo "Build failed"
    exit 1
fi
```

✅ **Do:**
```bash
call_task_with_status "Build Dev Images" "release:build:dev" "" "CLI_ARGS=v1.0"
```

### 5. **Use Proper Error Handling**

❌ **Don't:**
```bash
test_function() {
    some_command
    another_command
    # No error checking
}
```

✅ **Do:**
```bash
test_function() {
    if ! some_command; then
        print_status "FAIL" "First command failed"
        return 1
    fi
    
    if ! another_command; then
        print_status "FAIL" "Second command failed"
        return 1
    fi
    
    print_status "PASS" "All commands successful"
    return 0
}
```

### 6. **Use Waiting Functions**

❌ **Don't:**
```bash
# Fixed sleep
docker run -d --name registry registry:2
sleep 10  # Hope it's ready
```

✅ **Do:**
```bash
docker run -d --name registry registry:2
wait_for_condition \
    "curl -s 'http://localhost:5000/v2/'" \
    30 \
    1 \
    "registry"
```

## Common Patterns

### 1. Docker Container Testing

```bash
test_docker_functionality() {
    local container_name="test-container-$$"
    
    # Start container
    print_status "INFO" "Starting test container..."
    if ! docker run -d --name "$container_name" nginx; then
        print_status "FAIL" "Failed to start container"
        return 1
    fi
    
    # Register cleanup for this specific container
    register_cleanup "docker_cleanup_container '$container_name'"
    
    # Wait for container to be ready
    if ! wait_for_condition "docker exec $container_name curl -s localhost" 30 1 "nginx"; then
        print_status "FAIL" "Container not ready"
        return 1
    fi
    
    # Test functionality
    if docker exec "$container_name" curl -s localhost | grep -q "Welcome to nginx"; then
        print_status "PASS" "Container serving content"
        return 0
    else
        print_status "FAIL" "Container not serving expected content"
        return 1
    fi
}
```

### 2. Task Testing

```bash
test_build_tasks() {
    local test_tag="test-$(date +%s)"
    
    # Set environment
    export REGISTRY_URL="localhost:5000"
    export IMAGE_TAG="$test_tag"
    
    # Test dev build
    if ! call_task_with_status "Dev Build" "release:build:dev" "" "CLI_ARGS=$test_tag"; then
        return 1
    fi
    
    # Verify images exist
    if docker images | grep -q "$REGISTRY_URL/.*:$test_tag"; then
        print_status "PASS" "Images built successfully"
        return 0
    else
        print_status "FAIL" "Images not found"
        return 1
    fi
}
```

### 3. File System Testing

```bash
test_file_operations() {
    local test_dir="/tmp/test-$$"
    
    # Create test directory
    if ! mkdir -p "$test_dir"; then
        print_status "FAIL" "Failed to create test directory"
        return 1
    fi
    
    # Register cleanup
    register_cleanup "rm -rf '$test_dir'"
    
    # Test file operations
    if echo "test content" > "$test_dir/test.txt"; then
        print_status "PASS" "File created successfully"
    else
        print_status "FAIL" "Failed to create file"
        return 1
    fi
    
    # Verify content
    if [[ "$(cat "$test_dir/test.txt")" == "test content" ]]; then
        print_status "PASS" "File content correct"
        return 0
    else
        print_status "FAIL" "File content incorrect"
        return 1
    fi
}
```

## Available Functions

### Logging Functions

```bash
print_status "LEVEL" "message" [exit_on_fail]
log_info "message"
log_success "message"
log_error "message"
log_warning "message"
log_debug "message"
```

### Test Management

```bash
init_test_suite "name"
run_test_case "description" function_name
complete_test_suite exit_code
register_cleanup cleanup_function
```

### Docker Utilities

```bash
docker_cleanup_container "container_name"
docker_exec_as_user "container" "user" "workdir" "command"
```

### Task Utilities

```bash
call_task "task_name" ["taskfile"] ["args"]
call_task_with_status "description" "task_name" ["taskfile"] ["args"]
check_task_exists "task_name" ["taskfile"]
```

### Condition Utilities

```bash
wait_for_condition "command" max_attempts interval "description"
check_command "command_name"
check_file_exists "file_path"
check_port_available port_number
```

### Project Utilities

```bash
find_project_root
validate_project_root
```

## Examples

### Complete Test Example

```bash
#!/bin/bash

# Source framework
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/../BashTestCase.sh"

# Initialize
init_test_suite "Complete Example Test"

# Global variables
TEST_CONTAINER="example-test-$$"
TEST_REGISTRY="localhost:5000"

# Cleanup function
cleanup_example_test() {
    print_status "INFO" "Cleaning up example test..."
    docker_cleanup_container "$TEST_CONTAINER"
    docker_cleanup_container "test-registry-$$"
}

# Register cleanup
register_cleanup cleanup_example_test

# Test functions
test_environment_setup() {
    print_status "INFO" "Setting up test environment..."
    
    # Check prerequisites
    if ! check_command "docker"; then
        print_status "FAIL" "Docker not available"
        return 1
    fi
    
    if ! check_command "task"; then
        print_status "FAIL" "Task not available"
        return 1
    fi
    
    print_status "PASS" "Environment setup complete"
    return 0
}

test_registry_functionality() {
    print_status "INFO" "Testing registry functionality..."
    
    # Start registry
    if ! docker run -d --name "test-registry-$$" -p 5000:5000 registry:2; then
        print_status "FAIL" "Failed to start registry"
        return 1
    fi
    
    # Wait for registry
    if ! wait_for_condition "curl -s 'http://$TEST_REGISTRY/v2/'" 30 1 "registry"; then
        print_status "FAIL" "Registry not ready"
        return 1
    fi
    
    print_status "PASS" "Registry functionality verified"
    return 0
}

test_build_and_push() {
    print_status "INFO" "Testing build and push..."
    
    local test_tag="test-$(date +%s)"
    export REGISTRY_URL="$TEST_REGISTRY"
    export IMAGE_TAG="$test_tag"
    
    # Test build
    if ! call_task_with_status "Build Images" "release:build:dev" "" "CLI_ARGS=$test_tag"; then
        return 1
    fi
    
    # Test push
    if ! call_task_with_status "Push Images" "release:push:dev" "" "CLI_ARGS=$test_tag"; then
        return 1
    fi
    
    # Verify in registry
    if curl -s "http://$TEST_REGISTRY/v2/_catalog" | grep -q "salesto"; then
        print_status "PASS" "Images found in registry"
        return 0
    else
        print_status "FAIL" "Images not found in registry"
        return 1
    fi
}

# Run tests
run_test_case "Environment Setup" test_environment_setup
run_test_case "Registry Functionality" test_registry_functionality
run_test_case "Build and Push" test_build_and_push

# Complete
complete_test_suite $?
```

## Troubleshooting

### Common Issues

1. **Tests fail with "command not found"**
   - Ensure the framework is sourced correctly
   - Check the path to BashTestCase.sh

2. **Cleanup not working**
   - Verify cleanup function is registered before resources are created
   - Check cleanup function syntax

3. **Docker containers not cleaned up**
   - Use `docker_cleanup_container` instead of manual cleanup
   - Register cleanup before starting containers

4. **Tests hang indefinitely**
   - Use `wait_for_condition` instead of fixed sleeps
   - Set appropriate timeouts

5. **Inconsistent test results**
   - Use unique names for test resources (e.g., append `$$` for process ID)
   - Ensure proper cleanup between test runs

### Debug Mode

Run tests with debug output:

```bash
./my_test.sh --debug
```

This provides additional information about:
- Framework initialization
- Command execution
- Cleanup operations
- Resource management

### Manual Cleanup

If tests leave resources behind:

```bash
# Clean up containers
docker ps -a | grep test | awk '{print $1}' | xargs docker rm -f

# Clean up images
docker images | grep test | awk '{print $3}' | xargs docker rmi -f

# Clean up networks
docker network ls | grep test | awk '{print $1}' | xargs docker network rm
```

## Contributing

When adding new functionality to the framework:

1. **Follow naming conventions**: Use descriptive function names with consistent prefixes
2. **Add documentation**: Include usage examples in function comments
3. **Test thoroughly**: Ensure new functions work in various scenarios
4. **Update this guide**: Add examples and best practices for new features

## File Organization

```
tests/
├── BashTestCase.sh          # Framework core
├── BashTestCase.md          # This guide
├── setup/
│   ├── README.md
│   └── test_setup.sh
└── taskfile/
    └── release/
        ├── README.md
        ├── test_release.sh
        ├── test_release_simple.sh
        └── validate_environment.sh
```

## Integration with CI/CD

The framework is designed to work well in CI/CD environments:

```yaml
# GitHub Actions example
- name: Run Tests
  run: |
    chmod +x tests/**/*.sh
    ./tests/setup/test_setup.sh
    ./tests/taskfile/release/test_release.sh
```

The framework provides:
- Clear exit codes (0 for success, 1 for failure)
- Structured output for parsing
- Automatic cleanup even on failures
- Consistent logging format