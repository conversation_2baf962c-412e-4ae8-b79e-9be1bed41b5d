version: '3'

vars:
  DEFAULT_DEV_TAG: "dev"
  DEFAULT_PROD_TAG: "prod"
  DEFAULT_ALL_TAG: "latest"
  BUILDER_OPTION: '{{if .BUILDX_BUILDER}}--builder {{.BUILDX_BUILDER}}{{end}}'
  BUILDER_ARG: '{{if .BUILDX_BUILDER}}{{.BUILDX_BUILDER}}{{end}}'

tasks:
  slim:
    desc: "Slim down images"
    requires:
      vars: [ TAG ]
    cmds:
      - docker context use default
      - slim build --target {{.TAG}} --tag {{.TAG}} --http-probe=false {{.ARGS}}

  build:dev:
    desc: Build development images
    vars:
      TAG: '{{.CLI_ARGS | default .DEFAULT_DEV_TAG}}'
    cmds:
      - DOCKER_BUILDKIT=1 IMAGE_TAG={{.TAG}} docker compose -f docker-compose.dev.yml build

  build:prod:
    desc: Build production images
    vars:
      TAG: '{{.CLI_ARGS | default .DEFAULT_PROD_TAG}}'
    cmds:
      - echo "Checking AMD64 compatibility..."
      - |
        docker buildx inspect {{.BUILDER_ARG}} --bootstrap || echo "Warning: Builder {{.BUILDER_ARG}} not found or not available"
        docker buildx inspect {{.BUILDER_ARG}} | grep -q "linux/amd64" || echo "Warning: AMD64 platform not supported by builder {{.BUILDER_ARG}}"
      - DOCKER_BUILDKIT=1 IMAGE_TAG={{.TAG}} docker compose -f docker-compose.prod.yml build {{.BUILDER_OPTION}}

  push:dev:
    desc: Push development images
    vars:
      TAG: '{{.CLI_ARGS | default .DEFAULT_DEV_TAG}}'
    cmds:
      - IMAGE_TAG={{.TAG}} docker compose -f docker-compose.dev.yml push

  push:prod:
    desc: Push production images
    vars:
      TAG: '{{.CLI_ARGS | default .DEFAULT_PROD_TAG}}'
    cmds:
      - IMAGE_TAG={{.TAG}} docker compose -f docker-compose.prod.yml push

  build-and-push:dev:
    desc: Build and push development images
    vars:
      TAG: '{{.CLI_ARGS | default .DEFAULT_DEV_TAG}}'
    cmds:
      - task: build:dev
        vars: {CLI_ARGS: '{{.TAG}}'}
      - task: push:dev
        vars: {CLI_ARGS: '{{.TAG}}'}

  build-and-push:prod:
    desc: Build and push production images
    vars:
      TAG: '{{.CLI_ARGS | default .DEFAULT_PROD_TAG}}'
    cmds:
      - task: build:prod
        vars: {CLI_ARGS: '{{.TAG}}'}
      - task: push:prod
        vars: {CLI_ARGS: '{{.TAG}}'}

  build:all:
    desc: Build both dev and prod images
    vars:
      TAG: '{{.CLI_ARGS | default .DEFAULT_ALL_TAG}}'
    cmds:
      - task: build:dev
        vars: {CLI_ARGS: '{{.TAG}}'}
      - task: build:prod
        vars: {CLI_ARGS: '{{.TAG}}'}

  push:all:
    desc: Push both dev and prod images
    vars:
      TAG: '{{.CLI_ARGS | default .DEFAULT_ALL_TAG}}'
    cmds:
      - task: push:dev
        vars: {CLI_ARGS: '{{.TAG}}'}
      - task: push:prod
        vars: {CLI_ARGS: '{{.TAG}}'}

  build:sandbox:
    desc: Build sandbox image
    vars:
      TAG: '{{.CLI_ARGS | default .DEFAULT_DEV_TAG}}'
    cmds:
      - |
        echo "Building sandbox image: {{.TAG}}" >&2
        docker build \
          --tag {{.TAG}} \
          --file Dockerfile \
          --target sandbox \
          . >&2
        echo "{{.TAG}}"

  push:sandbox:
    desc: Push sandbox image
    vars:
      TAG: '{{.CLI_ARGS | default .DEFAULT_DEV_TAG}}'
    cmds:
      - docker push {{.TAG}}

  build-and-push:sandbox:
    desc: Build and push sandbox image
    vars:
      TAG: '{{.CLI_ARGS | default .DEFAULT_DEV_TAG}}'
    cmds:
      - task: build:sandbox
        vars: {CLI_ARGS: '{{.TAG}}'}
      - task: push:sandbox
        vars: {CLI_ARGS: '{{.TAG}}'}

  release:
    desc: Build and push both dev and prod images
    vars:
      TAG: '{{.CLI_ARGS | default .DEFAULT_ALL_TAG}}'
    cmds:
      - task: build-and-push:dev
        vars: {CLI_ARGS: '{{.TAG}}'}
      - task: build-and-push:prod
        vars: {CLI_ARGS: '{{.TAG}}'}

  release:sandbox:
    desc: Build and push sandbox image
    vars:
      TAG: '{{.CLI_ARGS | default .DEFAULT_DEV_TAG}}'
    cmds:
      - task: build-and-push:sandbox
        vars: {CLI_ARGS: '{{.TAG}}'}

  release:all:
    desc: Build and push dev, prod, and sandbox images
    vars:
      TAG: '{{.CLI_ARGS | default .DEFAULT_ALL_TAG}}'
    cmds:
      - task: build-and-push:dev
        vars: {CLI_ARGS: '{{.TAG}}'}
      - task: build-and-push:prod
        vars: {CLI_ARGS: '{{.TAG}}'}
      - task: build-and-push:sandbox
        vars: {CLI_ARGS: '{{.TAG}}'}
