# Testing

- run test in docker via [taskfile.tests.yaml](../taskfile.tests.yaml)
- write tests with a pest framework
- write bash tests using [BashTestCase framework](../tests/BashTestCase.md)

## Mock & Spy & Stub

- prefer london over detroit school and try to avoid mocking
- create a Mock, Spy or Stub implementation for tests and keep it in tests/Mock, tests/Spy, tests/Stub

## Datasets
Use datasets for tests with multiple inputs and expected outputs.
```
it('has emails', function (string $email) {
    expect($email)->not->toBeEmpty();
})->with(['<EMAIL>', '<EMAIL>']);
```

## Feature tests

### Feature - Directory structure

- `tests/Feature/[ModuleName]/[FeatureName]Test.php`

### Feature - Tests Approach

- first via Filament `livewire()`
- second via Http `test()->get()`

### Feature - Tests Assertions

- response status and content via snapshots
- count database entries before and after
- simple assertions focusing on the goal without checking every property and implementation detail

### Feature - What to test
Use snapshots when possible for asserting:
- validation
- UI rendering
- database entries
- notifications
- emails

## Integration tests

### Integration - Directory structure

- `tests/Integration/[ModuleName]/**/[ClassName]Test.php`

### Integration - Tests Approach

- use database
- test whole complexity
- as many scenarios as possible

### Integration - External HTTP calls

- use History Middleware & Mock Handler from https://docs.guzzlephp.org/en/stable/testing.html

### Integration - Tests Assertions

- use snapshots when possible
- count database entries before and after
- event dispatching
- assert result
- do not assert implementation details

### Integration - What to test

- complex flows
- external integrations

## Unit tests

### Unit - Directory structure

- `tests/Unit/[ModuleName]/**/[ClassName]Test.php`

### Unit - Tests Approach

- do not use database
- test one class
- test one method
- test one scenario

### Unit - Tests Assertions

- use snapshots when possible
- event dispatching
- assert result
- do not assert implementation details

### Unit - What to test

- complex business logic
- complex calculations

## General principles

### Asssertions

- try to group and encapsulate assertions in function with a descriptive name
- try to use assertions from pestphp/pest-plugin-laravel
- try to use assertions from pestphp/pest-plugin-livewire
- try to use assertions from pestphp/pest

### Factory & Seed & ObjectMother

- use Laravel model factory to create easy a full Aggregate with entities in just one line of code in a test
- object mother pattern for creating objects always comme out from AggregateRoot 
- do not create standalone entities 

### Snapshots Handling Dynamic Data

```
expect()->pipe('toMatchSnapshot', function (Closure $next) {
    if (is_string($this->value)) {
        $this->value = preg_replace(
            '/name="_token" value=".*"/',
            'name="_token" value="my_test"',
            $this->value
        );
    }
 
    return $next();
});
```

## Bash Tests

### Bash Tests - Framework

- use [BashTestCase framework](../tests/BashTestCase.md) for all bash tests
- follow the best practices guide for consistent test structure
- use framework utilities instead of manual implementations

### Bash Tests - Directory Structure

```
tests/
├── BashTestCase.sh          # Framework core
├── BashTestCase.md          # Best practices guide
├── setup/
│   ├── README.md
│   └── test_setup.sh        # Setup verification tests
└── taskfile/
    └── release/
        ├── README.md
        ├── test_release.sh          # Full release tests
        ├── test_release_simple.sh   # Simple release tests
        └── validate_environment.sh  # Environment validation
```

### Bash Tests - Approach

- **Always source the framework**: `source "$SCRIPT_DIR/../BashTestCase.sh"`
- **Initialize test suite**: `init_test_suite "Test Name"`
- **Register cleanup**: `register_cleanup cleanup_function`
- **Use test cases**: `run_test_case "Description" test_function`
- **Complete properly**: `complete_test_suite $?`

### Bash Tests - Best Practices

- Use framework logging functions (`print_status`, `log_info`, etc.)
- Use framework utilities (`call_task`, `wait_for_condition`, etc.)
- Always register cleanup for resources (containers, files, etc.)
- Use descriptive test case names
- Return proper exit codes (0 for success, 1 for failure)
- Use `wait_for_condition` instead of fixed sleeps
- Use unique resource names (append `$$` for process ID)

### Bash Tests - Common Patterns

```bash
# Task testing
call_task_with_status "Build Images" "release:build:dev" "" "CLI_ARGS=v1.0"

# Docker testing
docker_cleanup_container "test-container"
wait_for_condition "curl -s 'http://localhost:5000/v2/'" 30 1 "registry"

# Condition testing
check_command "docker"
check_task_exists "release:build:dev"
```

### Bash Tests - What to Test

- **Setup scripts**: Verify complete environment setup works
- **Task workflows**: Test taskfile tasks and their combinations
- **Docker operations**: Container lifecycle, image building, registry operations
- **Environment validation**: Check prerequisites and configurations
- **Integration flows**: End-to-end workflows combining multiple components


