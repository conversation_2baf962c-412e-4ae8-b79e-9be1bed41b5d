## Project description

System to manage multiples sales channels in one place - managing orders, parcels, offers, products, warehouse, client notifications, invoicing, receipts and etc.

## International

- remeber about use transtalions `__d('[module]','[message]')`
- multiple currencies
- keep all dates in UTC multiple timezones
- display all dates in tenant's (team) local timezone; use

## Comments

- Do not add coments for methods
- do not add redundands comments above code when the names call the same meaning

## Migrations

- comments for columns in english
- every module has own PostgreSQL schema PostgresTrait
- remeber about adding a new schema at Laravel's databases.php->connections.pgsql.search_path

## Multi tenant

Application is multi-tenatn.
Every resource should belongs to a tenant.
Maybe a good approach is global scope to filter every resource by a tenant id of current user.
User may has to multiple tenants.
We should has possibility to switch a tenant globally at header via a select.
Follow https://filamentphp.com/docs/3.x/panels/tenancy

## Synchornization with externals

Synchronizations in both sides with integrated external systems - stores, marketplaces, warehouses etc.

## Domain models and data structures

- best and verified archetypes of all domain like Order, Product, Client, Warehouse, Parcel etc.
- inserting over deleting or updating entities
- using application events to notify other modules about changes in the system

## Tech stack

- Laravel
- Filament
- Filament
- PostgreSQL

## System Architecture

- modular monolith
- domains splited on bounded contexts in ./src
- must not be any eloquent's integration between model in different modules

## Modules integration

- integrations between modules only via facades in ./src/\*\*/UI/Facade
- no direct access to a module's tables from outside of modules
- create a Facade when needed during integration

## Application Architecture

- Every action via Laravel Jobs it allows to keep dispatched jobs as a audit logs
- hexagonal architecture
  -- UI for Filament, Controllers, Listeners
  -- Application for appilcation logic cordinating transactions and dipatching events
  -- Domain for Eloqent models with data manipulation only via @AggregateRoot
  -- Infrestructure for Integrations with other modules etc
- Port-Adapter
  -- similar to hexagonal but for technical modules
- clean code principles
- SOLID principles
- GRASP principles

## Domain Driven Design (DDD)

- without repository patters - eloquent is used directly in each module
- mark eloquent model as Aggregate Root with @AggregateRoot annotation
- mark eloquent model as Entity with @Entity annotation
- use aggregates root methods for changing state of entity
- do not operate on entities directly

## UI

- filament as UI
- when some relation between two aggregate roots is needed within a one module, create a inheritance for a model at ./src/\*\*/UI/Filament/Model and create a eloquent relationship there.
- there should be no application or domain logic on filament side; Filament's should be treated as a frontend/controller/trigger


## PHP

- omit dockblock when argumens or return types all known unless the Laravel's ide-helper comments

## Consistency

- use optimistic locking to accomplish consistency

# Money & Currency

- use MoneyCalculator for calculations
- use MoneyCalculator for formating
