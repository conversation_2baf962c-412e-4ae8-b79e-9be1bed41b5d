version: '3'

tasks:
    all:
        desc: 'Run all Pest tests'
        cmds:
            - task exec -- "vendor/bin/pest"

    unit:
        desc: 'Run unit tests'
        cmds:
            - task exec -- "vendor/bin/pest --group=unit"

    feature:
        desc: 'Run feature tests'
        cmds:
            - task exec -- "vendor/bin/pest --group=feature"

    file:
        desc: 'Run tests in a specific file'
        cmds:
            - task exec -- "vendor/bin/pest {{.CLI_ARGS}}"

    filter:
        desc: 'Run a specific test method'
        cmds:
            - task exec -- "vendor/bin/pest --filter={{.CLI_ARGS}}"

    debug:
        desc: 'Run all tests with Xdebug enabled'
        cmds:
            - docker compose exec -e XDEBUG_MODE=debug -e XDEBUG_SESSION=1 php vendor/bin/pest

    refresh:
        cmds:
            - task artisan -- db:wipe --env testing
            - task artisan -- migrate:fresh --env testing

    debug:file:
        desc: 'Run tests in a specific file with Xdebug enabled'
        cmds:
            - docker compose exec -e XDEBUG_MODE=debug -e XDEBUG_SESSION=1 php vendor/bin/pest {{.CLI_ARGS}}

    debug:filter:
        desc: 'Run a specific test method with Xdebug enabled'
        cmds:
            - docker compose exec -e XDEBUG_MODE=debug -e XDEBUG_SESSION=1 php vendor/bin/pest --filter={{.CLI_ARGS}}

    allegro:oauth:server:
      desc: 'Run the Allegro OAuth Server (handles authorization and token exchange)'
      cmds:
        - task php:run -- -S 0.0.0.0:8383 tests/Integration/Integration/Allegro/server.php

    setup:
      desc: 'Run all setup tests'
      cmds:
        - ./tests/setup/test_setup.sh
