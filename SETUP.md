# Environment Setup Scripts

This repository includes automated setup and testing scripts to ensure a consistent development environment.

## Scripts Overview

### setup.sh
The main setup script that prepares your development environment from scratch.

**What it does:**
- Checks and installs required tools (Task<PERSON><PERSON>, Docker, Docker Compose)
- Builds Docker images using `task build`
- Starts the application using `task up`
- Runs tests using `task tests:all` to verify everything works

### setup.test.sh
A comprehensive testing script that validates the setup in a clean Ubuntu container using Docker-in-Docker.

**What it does:**
- Creates a fresh Ubuntu 22.04 container
- Installs Docker inside the container
- Runs the setup.sh script inside the container
- Performs comprehensive verification tests
- Cleans up the test container automatically

## Usage

### Quick Setup (Local Environment)
```bash
# Make scripts executable (if not already)
chmod +x setup.sh setup.test.sh

# Run the setup
./setup.sh
```

### Test Setup in Clean Environment
```bash
# Test the complete setup process in a clean Ubuntu container
./setup.test.sh
```

## Requirements

### Host Machine Requirements
- Docker installed and running
- Internet connection for downloading dependencies

### Supported Platforms
- **macOS**: Uses Homebrew when available, falls back to manual installation
- **Linux**: Uses package managers and direct downloads
- **Ubuntu containers**: Optimized for containerized environments

## What Gets Installed

### Tools
- **Taskfile**: Task runner for managing development workflows
- **Docker**: Container platform (if not already installed)
- **Docker Compose**: Multi-container Docker applications

### Application Setup
- Docker images built for all services
- All containers started (PHP, PostgreSQL, HTTP)
- Database migrations and setup
- Full test suite execution

## Test Coverage

The setup.test.sh script performs the following verification tests:

### 1. Required Tools
- ✅ Taskfile installation and functionality
- ✅ Docker installation and running status
- ✅ Docker Compose plugin availability

### 2. Taskfile Configuration
- ✅ Taskfile validity
- ✅ Required tasks existence (build, up, tests)

### 3. Docker Containers
- ✅ PHP container running
- ✅ PostgreSQL container running
- ✅ PostgreSQL testing container running
- ✅ HTTP container running

### 4. Container Health
- ✅ PHP container responding
- ✅ PostgreSQL database connectivity
- ✅ PostgreSQL testing database connectivity

### 5. Laravel Application
- ✅ Composer dependencies installed
- ✅ Laravel Artisan functionality
- ✅ Database connection working

### 6. Application Tests
- ✅ Full test suite execution
- ✅ All tests passing

### 7. HTTP Service
- ✅ Nginx configuration validity
- ✅ Web server accessibility (when possible)

## Docker-in-Docker Testing

The setup.test.sh script uses Docker-in-Docker (DinD) to create an isolated testing environment:

```
Host Machine
├── Docker Engine
└── Test Container (Ubuntu 22.04)
    ├── Docker Engine (installed during test)
    ├── Project Files (mounted)
    └── Application Containers
        ├── PHP
        ├── PostgreSQL
        ├── PostgreSQL Testing
        └── HTTP
```

This approach ensures:
- **Clean Environment**: Each test starts with a fresh Ubuntu installation
- **Isolation**: Tests don't interfere with your local environment
- **Reproducibility**: Same results across different machines
- **CI/CD Ready**: Can be easily integrated into automated pipelines

## Troubleshooting

### Common Issues

1. **Docker not running**
   ```bash
   # Start Docker Desktop (macOS/Windows)
   # Or start Docker service (Linux)
   sudo systemctl start docker
   ```

2. **Permission denied errors**
   ```bash
   # Make scripts executable
   chmod +x setup.sh setup.test.sh
   ```

3. **Port conflicts**
   ```bash
   # Stop conflicting services
   task down
   # Check what's using the ports
   lsof -i :80 -i :5432 -i :5433
   ```

4. **Test container cleanup**
   ```bash
   # Manual cleanup if needed
   docker rm -f salesto-setup-test
   ```

### Getting Help

If you encounter issues:

1. Check the colored output for specific error messages
2. Review the troubleshooting steps provided by the scripts
3. Check container logs: `docker compose logs`
4. Verify .env files are properly configured

## Integration with CI/CD

The setup.test.sh script is designed to work in CI/CD environments:

```yaml
# Example GitHub Actions workflow
- name: Test Environment Setup
  run: ./setup.test.sh
```

The script will:
- Exit with code 0 on success
- Exit with code 1 on failure
- Provide detailed logging for debugging
- Clean up resources automatically

## Development Workflow

After successful setup, use these commands:

```bash
# Start the application
task up

# Stop the application
task down

# Run tests
task tests:all

# Access PHP container
task sh

# Run Laravel commands
task artisan -- migrate

# Run Composer commands
task composer -- install
```