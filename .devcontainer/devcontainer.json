{
    "name": "Salesto PHP Development",
    "dockerComposeFile": "../docker-compose.yaml",
    "service": "php",
    "workspaceFolder": "/var/www/html",
    "customizations": {
        "vscode": {
            "extensions": [
                "m1guelpf.better-pest",
                "bmewburn.vscode-intelephense-client",
                "xdebug.php-debug",
                "junstyle.php-cs-fixer",
                "mehedidracula.php-namespace-resolver",
                "neilbrayfield.php-docblocker",
                "editorconfig.editorconfig",
                "mikestead.dotenv",
                "recca0120.vscode-phpunit",
                "esbenp.prettier-vscode",
                "dbaeumer.vscode-eslint"
            ],
            "settings": {
                // PHP settings
                "php.validate.executablePath": "/usr/local/bin/php",
                "php.suggest.basic": true,
                // IntelliSense settings
                "intelephense.environment.includePaths": [
                    "vendor"
                ],
                "intelephense.files.maxSize": 5000000,
                // Better Pest settings
                "better-pest.docker.enable": true,
                "better-pest.docker.command": "task exec --",
                "better-pest.pest.binary": "vendor/bin/pest",
                "better-pest.testDirectory": "tests",
                "better-pest.showTerminalOnRun": true,
                "better-pest.testExplorer.enabled": true,
                "better-pest.testExplorer.showClassNames": true,
                "better-pest.testExplorer.showSkipped": true,
                "better-pest.pathMappings": {
                    "/var/www/html": "${workspaceFolder}"
                },
                // PHP Debug settings
                "php-debug.xdebugSettings": {
                    "max_nesting_level": 500
                },
                // PHP CS Fixer settings
                "php-cs-fixer.executablePath": "${workspaceFolder}/vendor/bin/php-cs-fixer",
                "php-cs-fixer.onsave": true,
                "php-cs-fixer.config": ".php-cs-fixer.php",
                // PHP Namespace Resolver settings
                "namespaceResolver.sortAlphabetically": true,
                "namespaceResolver.leadingSeparator": true,
                // Editor settings
                "editor.formatOnSave": true,
                "editor.codeActionsOnSave": {
                    "source.fixAll": "explicit"
                },
                // Terminal settings
                "terminal.integrated.defaultProfile.linux": "bash",
                "terminal.integrated.profiles.linux": {
                    "bash": {
                        "path": "bash",
                        "icon": "terminal-bash"
                    }
                }
            }
        }
    },
    "remoteUser": "salesto",
    "postStartCommand": "composer install",
    "forwardPorts": [
        5173
    ],
    "portsAttributes": {
        "5173": {
            "label": "Vite Dev Server",
            "onAutoForward": "notify"
        }
    },
    "otherPortsAttributes": {
        "onAutoForward": "silent"
    }
}