<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('integration.configurations', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->json('configuration');
            $table->foreignUuid('integration_id')->constrained('integration.integrations');
            $table->softDeletes();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('integration.configurations');
    }
};
