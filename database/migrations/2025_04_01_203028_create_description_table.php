<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('product.description', function (Blueprint $table) {
            $table->uuid('id')->primary();

            $table->string('title');
            $table->longText('content')->nullable();

            $table->foreignUuid('product_id')->nullable();

            $table->softDeletes();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('product.description');
    }
};
