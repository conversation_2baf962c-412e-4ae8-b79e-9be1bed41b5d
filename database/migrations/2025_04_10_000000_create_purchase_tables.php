<?php

use App\Traits\PostgresTrait;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    use PostgresTrait;

    public function up(): void
    {
        // Create purchase schema
        $this->createSchema('purchase');

        // Create orders table
        Schema::create('purchase.orders', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('currency', 3)->default('PLN')->comment('Currency for the entire order');
            $table->text('notes')->nullable()->comment('Additional order notes');
            $table->integer('version')->default(0)->comment('Version number for optimistic locking');
            $table->softDeletes();
            $table->timestamps();
        });

        // Create order items table
        Schema::create('purchase.order_items', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('order_id')->constrained('purchase.orders')->onDelete('cascade');
            $table->string('name')->comment('Product name');
            $table->string('sku')->nullable()->comment('Product SKU');
            $table->double('price')->comment('Unit price');
            $table->integer('quantity')->comment('Quantity');
            $table->double('total')->comment('Total price (price * quantity)');
            $table->json('attributes')->nullable()->comment('Additional product attributes');
            $table->softDeletes();
            $table->timestamps();

            // Index for faster lookups
            $table->index('order_id');
        });

        // Create order buyers table with versioning
        Schema::create('purchase.order_buyers', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('order_id')->constrained('purchase.orders')->onDelete('cascade');
            $table->string('first_name')->comment('Buyer\'s first name');
            $table->string('last_name')->comment('Buyer\'s last name');
            $table->string('email')->nullable()->comment('Buyer\'s email');
            $table->string('phone')->nullable()->comment('Buyer\'s phone number');
            $table->integer('version')->default(1)->comment('Version number for change tracking');
            $table->softDeletes();
            $table->timestamps();

            // Index for faster lookups
            $table->index(['order_id', 'version']);
        });

        // Create order addresses table with versioning
        Schema::create('purchase.order_addresses', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('order_id')->constrained('purchase.orders')->onDelete('cascade');
            $table->string('address')->comment('Full address (street, building, apartment)');
            $table->string('city')->comment('City');
            $table->string('postal_code')->comment('Postal code');
            $table->string('country')->comment('Country');
            $table->string('state')->nullable()->comment('State/province/region');
            $table->integer('version')->default(1)->comment('Version number for change tracking');
            $table->softDeletes();
            $table->timestamps();

            // Index for faster lookups
            $table->index(['order_id', 'version']);
        });

        // Create order billing table with versioning
        Schema::create('purchase.order_billings', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('order_id')->constrained('purchase.orders')->onDelete('cascade');
            $table->string('company_name')->comment('Company name');
            $table->string('tax_id')->comment('Tax ID');
            $table->string('company_address')->nullable()->comment('Company address');
            $table->integer('version')->default(1)->comment('Version number for change tracking');
            $table->softDeletes();
            $table->timestamps();

            // Index for faster lookups
            $table->index(['order_id', 'version']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('purchase.order_billings');
        Schema::dropIfExists('purchase.order_addresses');
        Schema::dropIfExists('purchase.order_buyers');
        Schema::dropIfExists('purchase.order_items');
        Schema::dropIfExists('purchase.orders');

        // Drop the purchase schema
        $this->dropSchema('purchase');
    }
};
