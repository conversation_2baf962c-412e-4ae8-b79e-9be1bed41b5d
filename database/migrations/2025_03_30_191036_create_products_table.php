<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Traits\PostgresTrait;

return new class extends Migration
{
    use PostgresTrait;

    public function up(): void
    {
        $this->createSchema('product');
        Schema::create('product.products', function (Blueprint $table) {
            $table->uuid('id')->primary();

            $table->string('name');

            $table->softDeletesDatetime();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('product.products');
    }
};
