<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Traits\PostgresTrait;

return new class extends Migration
{
    use PostgresTrait;

    public function up(): void
    {
        $this->createSchema('offer');
        Schema::create('offer.offers', function (Blueprint $table) {
            $table->uuid('id')->primary();

            $table->string('title');
            $table->longText('content')->nullable();

            $table->softDeletes();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('offer.offers');
    }
};
