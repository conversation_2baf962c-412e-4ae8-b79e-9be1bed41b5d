<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Traits\PostgresTrait;

return new class extends Migration
{
    use PostgresTrait;

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $this->createSchema('integration');
        Schema::create('integration.providers', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('name');
            $table->string('manifest');
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('integration.providers');
    }
};
