<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('integration.authorizations', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->json('authorization');
            $table->foreignUuid('integration_id')->constrained('integration.integrations');
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('integration.authorizations');
    }
};
