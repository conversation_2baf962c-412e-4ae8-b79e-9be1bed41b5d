<?php

namespace Database\Factories\Integration\Domain;

use Illuminate\Database\Eloquent\Factories\Factory;
use Integration\Domain\Provider;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<Provider>
 */
class ProviderFactory extends Factory
{
    protected $model = Provider::class;

    public function definition(): array
    {
        return [
            'name' => $this->faker->name(),
            'manifest' => $this->faker->name(),
        ];
    }
}
