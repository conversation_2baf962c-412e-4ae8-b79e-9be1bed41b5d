<?php

namespace Database\Factories\Integration\Domain;

use Illuminate\Database\Eloquent\Factories\Factory;
use Integration\Domain\Integration;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<Integration>
 */
class IntegrationFactory extends Factory
{
    protected $model = Integration::class;

    public function definition(): array
    {
        return [
            'name' => $this->faker->name(),
        ];
    }
}
