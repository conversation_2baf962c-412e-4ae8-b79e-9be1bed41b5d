<?php

namespace Database\Factories\Order\Domain;

use Illuminate\Database\Eloquent\Factories\Factory;
use Order\Domain\Order;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\Order\Domain\Order>
 */
class OrderFactory extends Factory
{
    protected $model = Order::class;

    public function definition(): array
    {
        return [
            'currency' => 'PLN',
        ];
    }

    public function withItem(?string $name = null, ?string $price = null, ?int $quantity = null): self
    {
        return $this->afterCreating(function (Order $order) use ($name, $price, $quantity) {
            $order->addItem(
                name: $name ?? $this->faker->words(3, true),
                price: (string) ($price ?? $this->faker->randomFloat(2, 10, 1000)),
                quantity: $quantity ?? $this->faker->numberBetween(1, 10)
            );
        });
    }
}
