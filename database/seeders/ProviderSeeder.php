<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Integration\Domain\Provider;
use Shared\Integration\Allegro\Port\AllegroIntegration;

class ProviderSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        Provider::forceCreate([
            'name' => 'Allegro',
            'manifest' => AllegroIntegration::class
        ]);
    }
}
