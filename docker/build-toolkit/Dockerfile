FROM docker:20.10.16

ENV HOME="/root"
ENV PYENV_ROOT="$HOME/.pyenv"
ENV PATH="$PYENV_ROOT/shims:$PYENV_ROOT/bin:$PATH"

# Install system dependencies in single layer
RUN apk add --no-cache \
    curl \
    git \
    bash \
    build-base \
    libffi-dev \
    openssl-dev \
    bzip2-dev \
    zlib-dev \
    readline-dev \
    sqlite-dev \
    && rm -rf /var/cache/apk/*

WORKDIR $HOME

# Install Python and tools in optimized layers
RUN git clone --depth=1 https://github.com/pyenv/pyenv.git .pyenv && \
    pyenv install 3.11 && \
    pyenv global 3.11 && \
    pip install --no-cache-dir ansible-core docker kubernetes jmespath

# Install Ansible collections
RUN ansible-galaxy collection install \
    google.cloud \
    community.general \
    community.docker \
    kubernetes.core \
    community.mongodb \
    && ansible-galaxy install elastic.elasticsearch,v7.17.0

# Copy external tools
COPY --from=alpine/helm:3.12.0 /usr/bin/helm /usr/bin/helm
COPY --from=bitnami/kubectl:1.27 /opt/bitnami/kubectl/bin/kubectl /usr/bin/kubectl
COPY --from=gcr.io/google.com/cloudsdktool/google-cloud-cli:372.0.0-alpine /google-cloud-sdk /usr/lib/google-cloud-sdk

# Configure tools and clean up
RUN helm plugin install https://github.com/databus23/helm-diff && \
    ln -s /usr/lib/google-cloud-sdk/bin/gcloud /usr/bin/gcloud && \
    ln -s /usr/lib/google-cloud-sdk/bin/docker-credential-gcloud /usr/bin/docker-credential-gcloud && \
    gcloud components reinstall --quiet && \
    gcloud components install gke-gcloud-auth-plugin --quiet && \
    rm -rf /tmp/* /root/.cache
