"[DOCKER] Build build-toolkit":
  extends:
    - .rules_for_deployments
  stage: docker
  needs: []
  variables:
    APP_NAME: "build-toolkit"
    CHANGES_IN_DIR: ".devops/docker/build-toolkit/**/*"
    WORKDIR: "$CI_PROJECT_DIR/.devops/docker/build-toolkit"
    APP_TAG: "latest"
    FORCE_BUILD: true
  image: docker:20.10.16
  services:
    - docker:20.10.16-dind
  before_script:
    - echo "$DOCKER_REGISTRY_PASSWORD" | docker login $DOCKER_REGISTRY --username $DOCKER_REGISTRY_USER --password-stdin
  script:
    - docker build -t $DOCKER_REGISTRY/$APP_NAME:$APP_TAG --network host --build-arg VERSION=$VERSION $WORKDIR
    - docker push $DOCKER_REGISTRY/$APP_NAME:$APP_TAG
