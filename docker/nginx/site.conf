server {

    listen 80;
    # IPv6 disabled due to system compatibility
    # listen [::]:80;

    # For https
    #listen 443 ssl;
    #listen [::]:443 ssl ipv6only=on;
    #ssl_certificate /etc/nginx/ssl/default.crt;
    #ssl_certificate_key /etc/nginx/ssl/default.key;

    root /var/www/html/public;
    index index.php;

    location / {
      try_files $uri @rewriteapp;
    }

    location @rewriteapp {
        rewrite ^(.*)$ /index.php/$1 last;
    }

    location ~ ^/(app|app_dev|config|index)\.php(/|$) {
        try_files $uri /index.php =404;
        fastcgi_pass php-upstream;
        fastcgi_index index.php;
        fastcgi_buffers 16 16k;
        fastcgi_buffer_size 32k;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_read_timeout 600;

        include fastcgi_params;
    }
}
