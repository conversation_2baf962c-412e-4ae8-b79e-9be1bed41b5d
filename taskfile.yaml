version: '3'

dotenv: [.env, .env.devops, .env.ai]

includes:
    github: taskfile.github.yaml
    buildx: taskfile.buildx.yaml
    release: taskfile.release.yaml
    tests: taskfile.tests.yaml
    db: taskfile.db.yaml
    ai: taskfile.ai.yaml
    sandbox: taskfile.sandbox.yaml

vars:
    CURRENT_DIR:
        sh: pwd | grep -o '[^/]*$'
    DOCKER_COMPOSE_FILES: |
        -f docker-compose.yaml \
    BUILD_ARGS: --build-arg USERID=${LOCAL_USERID} --build-arg GROUPID=${LOCAL_GROUPID} --build-arg LOCAL_USER=${LOCAL_USER}
    DOCKER_BUILDKIT: 'docker build ${BUILD_ARGS} -t buildkit --build-arg USERID=$(id -u) -q -f ./docker/php/Dockerfile .'

tasks:
    exec:
        desc: 'Run a command in the container'
        cmds:
            - docker compose exec php sh -c "{{.CLI_ARGS}}"

    artisan:
        silent: true
        cmds:
            - task exec -- php artisan {{.CLI_ARGS}}

    composer:
        silent: true
        cmds:
            - task exec -- composer {{.CLI_ARGS}}

    composer:dump:
        silent: true
        cmds:
            - task exec -- composer dumpautoload

    ide-helper:
        cmds:
            - task artisan -- ide-helper:eloquent
            - task artisan -- ide-helper:generate
            - task artisan -- ide-helper:meta
            - task artisan -- ide-helper:models -W

    build:
        cmds:
            - DOCKER_BUILDKIT=1 docker compose build {{.BUILD_ARGS}} {{.CLI_ARGS}}

    setup:
        desc: 'Setup the application with smart image management'
        cmds:
            - ./setup/setup-application.sh

    setup:test:
        desc: 'Test the image building logic'
        cmds:
            - ./setup/test-image-logic.sh

    build:force:
        cmds:
            - DOCKER_BUILDKIT=1 docker compose build {{.BUILD_ARGS}} --no-cache {{.CLI_ARGS}}

    up:
        cmds:
            - docker compose up -d

    refresh:
        cmds:
            - task up
            - task db:refresh

    stop:
        cmds:
            - docker compose stop

    down:
        cmds:
            - docker compose down {{.CLI_ARGS}}

    ps:
        cmds:
            - docker compose ps

    log:
        cmds:
            - docker compose up

    sh:
        cmds:
            - docker compose exec -it php sh

    sh:http:
        cmds:
            - docker compose exec -it http sh

    sh:run:
        cmds:
            - docker compose run -it php sh

    xdebug:on:
        desc: 'Enable Xdebug'
        cmds:
            - docker compose exec php sh -c "export XDEBUG_MODE=debug,develop && echo 'Xdebug enabled'"

    xdebug:off:
        desc: 'Disable Xdebug'
        cmds:
            - docker compose exec php sh -c "export XDEBUG_MODE=off && echo 'Xdebug disabled'"

    xdebug:toggle:
        desc: 'Toggle Xdebug on/off'
        cmds:
            - |
                if docker compose exec php sh -c "echo \$XDEBUG_MODE" | grep -q "debug"; then
                  task xdebug:off
                else
                  task xdebug:on
                fi

    php:
        desc: 'Run a PHP script'
        cmds:
            - task exec -- php {{.CLI_ARGS}}

    php:debug:
        desc: 'Run a PHP script with Xdebug enabled'
        cmds:
            - docker compose exec -e XDEBUG_MODE=debug -e XDEBUG_SESSION=1 php php {{.CLI_ARGS}}
