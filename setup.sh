#!/bin/bash

# setup.sh - Environment Setup Script
# This script sets up the complete development environment from scratch

set -e  # Exit on any error

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SETUP_DIR="$SCRIPT_DIR/setup"

# Source the toolkit
source "$SETUP_DIR/toolkit.sh"

# Main setup function
main() {
    show_header "Environment Setup" "Setting up the complete development environment from scratch"

    # Install Taskfile
    source "$SETUP_DIR/install-taskfile.sh"

    # Install Docker
    source "$SETUP_DIR/install-docker.sh"

    # Setup Application
    source "$SETUP_DIR/setup-application.sh"

    show_completion "Environment Setup"
}

# Run main function
main "$@"
