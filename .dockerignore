# Git
.git
.gitignore
.gitattributes

# Documentation
README.md
CHANGELOG.md
LICENSE
*.md

# Development files
.env*
!.env.prod
docker-compose*.yml
Dockerfile*
.dockerignore

# IDE
.vscode
.idea
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Node.js
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# PHP
vendor
.phpunit.result.cache

# Laravel
storage/app/*
!storage/app/.gitignore
storage/framework/cache/*
!storage/framework/cache/.gitignore
storage/framework/sessions/*
!storage/framework/sessions/.gitignore
storage/framework/testing/*
!storage/framework/testing/.gitignore
storage/framework/views/*
!storage/framework/views/.gitignore
storage/logs/*
!storage/logs/.gitignore
bootstrap/cache/*
!bootstrap/cache/.gitignore

# Testing
tests
phpunit.xml
.phpunit.cache

# Build artifacts
public/build
public/hot
public/storage

# Temporary files
*.tmp
*.temp
.cache