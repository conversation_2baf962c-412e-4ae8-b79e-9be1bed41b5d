#!/bin/bash

# setup-application.sh - Application setup script
# This script handles building and starting the application

# Source the toolkit
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/toolkit.sh"

# Function to check if image exists locally
image_exists_locally() {
    local image_name="$1"
    if docker image inspect "$image_name" >/dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

# Function to pull image from registry
pull_image() {
    local image_name="$1"
    log_info "Attempting to pull image: $image_name"

    if docker pull "$image_name" >/dev/null 2>&1; then
        log_success "Successfully pulled image: $image_name"
        return 0
    else
        log_warning "Failed to pull image: $image_name"
        return 1
    fi
}

# Function to get required images for current environment
get_required_images() {
    local registry_url="${REGISTRY_URL:-}"
    local image_tag="${IMAGE_TAG:-dev}"

    if [ -z "$registry_url" ]; then
        log_warning "REGISTRY_URL not set, skipping remote image checks"
        return 1
    fi

    # Determine which images are needed based on environment
    local images=()

    # Always need PHP image
    images+=("${registry_url}/php:${image_tag}")

    # Add HTTP image for production or if explicitly building it
    if [ "$image_tag" = "prod" ] || docker compose config --services 2>/dev/null | grep -q "http"; then
        images+=("${registry_url}/http:${image_tag}")
    fi

    printf '%s\n' "${images[@]}"
}

# Function to check and pull images if needed
check_and_pull_images() {
    log_info "Checking for existing images..."

    local images_to_build=()
    local all_images_available=true

    # Get list of required images
    local required_images
    if ! required_images=$(get_required_images); then
        log_info "Cannot determine required images, will build locally"
        return 1
    fi

    # Check each required image
    while IFS= read -r image_name; do
        [ -z "$image_name" ] && continue

        if image_exists_locally "$image_name"; then
            log_success "Image already exists locally: $image_name"
        else
            log_info "Image not found locally: $image_name"

            # Try to pull from registry
            if pull_image "$image_name"; then
                log_success "Image pulled successfully: $image_name"
            else
                log_warning "Could not pull image: $image_name"
                images_to_build+=("$image_name")
                all_images_available=false
            fi
        fi
    done <<< "$required_images"

    if [ "$all_images_available" = true ]; then
        log_success "All required images are available"
        return 0
    else
        log_info "Some images need to be built: ${images_to_build[*]}"
        return 1
    fi
}

# Function to build Docker images
build_images() {
    log_info "Checking image availability..."

    # First, try to use existing or pull images
    if check_and_pull_images; then
        log_success "All images are available, skipping build"
        return 0
    fi

    log_info "Building Docker images locally..."

    if task build; then
        log_success "Docker images built successfully"
        return 0
    else
        log_error "Failed to build Docker images"
        return 1
    fi
}

# Function to start the application
start_application() {
    log_info "Starting the application..."

    if task up; then
        log_success "Application started successfully"
        return 0
    else
        log_error "Failed to start the application"
        return 1
    fi
}

# Function to wait for services to be ready
wait_for_services() {
    log_info "Waiting for services to be ready..."

    for i in {1..10}; do
        log_info "Check iteration $i/10"

        docker compose ps

        all_healthy=true

        # Check if all services are running and healthy
        for service in $(docker compose config --services); do
            if docker compose ps --services --filter "status=running" | grep -q "$service"; then
                log_success "$service container is running"

                # Check health status if healthcheck is configured
                health_status=$(docker compose ps --format "table {{.Service}}\t{{.Status}}" | grep "$service" | awk '{print $2}')
                if echo "$health_status" | grep -q "healthy"; then
                    log_success "$service container is healthy"
                elif echo "$health_status" | grep -q "unhealthy"; then
                    log_warning "$service container is unhealthy"
                    log_info "Displaying last 10 lines of logs for $service container:"
                    docker compose logs --tail=10 "$service"
                    all_healthy=false
                elif echo "$health_status" | grep -q "starting"; then
                    log_info "$service container health check is starting..."
                    all_healthy=false
                fi
            else
                log_warning "$service container may not be running properly"
                log_info "Displaying last 10 lines of logs for $service container:"
                docker compose logs --tail=100 "$service"
                all_healthy=false
            fi
        done

        if [ "$all_healthy" = true ]; then
            log_success "All services are running and healthy"
            break
        fi

        # Wait 10 seconds before the next check
        sleep 10
    done
}

# Function to run tests
run_tests() {
    log_info "Running tests to verify setup..."

    if task tests:all; then
        log_success "All tests passed! Environment setup completed successfully"
        return 0
    else
        log_warning "Some tests failed, but the environment is set up. Check the test output above."
        return 1
    fi
}

# Function to show available tasks
show_available_tasks() {
    log_success "Setup completed!"
    log_info "Available tasks for this project:"
    echo
    task --list-all
}

# Main function for application setup
setup_application() {
    show_header "Application Setup" "Building and starting the application"

    # Build Docker images
    if ! build_images; then
        exit 1
    fi

    # Start the application
    if ! start_application; then
        exit 1
    fi

    # Wait for services to be ready
    wait_for_services

    # Run tests
    run_tests

    # Show available tasks
    show_available_tasks

    show_completion "Application Setup"
}

setup_application "$@"
