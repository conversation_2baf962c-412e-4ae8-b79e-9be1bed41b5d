#!/bin/bash

# install-taskfile.sh - Taskfile installation script
# This script handles Taskfile installation across different platforms

# Source the toolkit
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/toolkit.sh"

# Function to install Taskfile on macOS
install_taskfile_macos() {
    log_info "Installing Taskfile on macOS..."

    if command_exists brew; then
        brew install go-task/tap/go-task
    else
        log_warning "Homebrew not found. Installing Taskfile manually..."
        install_taskfile_manual "/usr/local/bin"
    fi
}

# Function to install Taskfile on Linux
install_taskfile_linux() {
    log_info "Installing Taskfile on Linux..."

    # Try different installation locations based on permissions
    if has_sudo && ! is_container; then
        log_info "Installing system-wide with sudo..."
        install_taskfile_manual_with_sudo "/usr/local/bin"
    elif [ -w "/usr/local/bin" ]; then
        log_info "Installing to /usr/local/bin (writable)..."
        install_taskfile_manual "/usr/local/bin"
    else
        log_info "Installing to user directory..."
        mkdir -p "$HOME/.local/bin"
        install_taskfile_manual "$HOME/.local/bin"
        # Add to PATH if not already there
        if ! echo "$PATH" | grep -q "$HOME/.local/bin"; then
            export PATH="$HOME/.local/bin:$PATH"
            echo 'export PATH="$HOME/.local/bin:$PATH"' >> "$HOME/.bashrc" 2>/dev/null || true
            log_info "Added $HOME/.local/bin to PATH"
        fi
    fi
}

# Function to install Taskfile manually using the official script
install_taskfile_manual() {
    local install_dir="$1"
    sh -c "$(curl --location https://taskfile.dev/install.sh)" -- -d -b "$install_dir"
}

# Function to install Taskfile manually with sudo
install_taskfile_manual_with_sudo() {
    local install_dir="$1"
    sudo sh -c "$(curl --location https://taskfile.dev/install.sh)" -- -d -b "$install_dir"
}

# Function to check if Taskfile is installed
check_taskfile_installed() {
    if command_exists task; then
        log_success "Taskfile is already installed"
        return 0
    else
        log_warning "Taskfile not found"
        return 1
    fi
}

# Function to install Taskfile based on OS
install_taskfile() {
    local os=$(detect_os)

    case $os in
        "macos")
            install_taskfile_macos
            ;;
        "linux")
            install_taskfile_linux
            ;;
        *)
            log_error "Unsupported operating system: $OSTYPE"
            exit 1
            ;;
    esac
}

# Function to verify Taskfile installation
verify_taskfile() {
    log_info "Verifying Taskfile installation..."

    if ! verify_command "task" "Taskfile"; then
        log_error "Taskfile installation failed"
        return 1
    fi

    # Test if Taskfile can read the project's Taskfile
    if task --list >/dev/null 2>&1; then
        log_success "Taskfile is working correctly with project configuration"
    else
        log_warning "Taskfile is installed but may have issues with project configuration"
    fi

    return 0
}

# Main function for Taskfile setup
setup_taskfile() {
    show_header "Taskfile Setup" "Installing and configuring Taskfile"

    # Check and install Taskfile
    if ! check_taskfile_installed; then
        install_taskfile
    fi

    # Verify installation
    if ! verify_taskfile; then
        log_error "Taskfile setup failed"
        exit 1
    fi

    show_completion "Taskfile Setup"
}

setup_taskfile "$@"
