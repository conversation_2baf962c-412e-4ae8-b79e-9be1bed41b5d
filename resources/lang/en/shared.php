<?php

return [
    // PhoneNumber translations
    'Phone number should not start with +. Use fromE164() for E.164 formatted numbers or provide prefix separately.' => 'Phone number should not start with +. Use fromE164() for E.164 formatted numbers or provide prefix separately.',
    'Phone number must contain only digits' => 'Phone number must contain only digits',
    'E.164 phone number must start with +' => 'E.164 phone number must start with +',
    'Phone number with + prefix must be in E.164 format (e.g., +123456789)' => 'Phone number with + prefix must be in E.164 format (e.g., +123456789)',
    'Invalid E.164 format' => 'Invalid E.164 format',
    'Invalid phone number format. Without a country prefix, the first digit must be 1-9.' => 'Invalid phone number format. Without a country prefix, the first digit must be 1-9.',
    'Phone number cannot be empty' => 'Phone number cannot be empty',
    'Phone number must start with 1-9' => 'Phone number must start with 1-9',
    
    // Country prefixes
    '+1 (United States & Canada)' => '+1 (United States & Canada)',
    '+44 (United Kingdom)' => '+44 (United Kingdom)',
    '+48 (Poland)' => '+48 (Poland)',
    '+49 (Germany)' => '+49 (Germany)',
    '+33 (France)' => '+33 (France)',
    '+34 (Spain)' => '+34 (Spain)',
    '+39 (Italy)' => '+39 (Italy)',
    '+31 (Netherlands)' => '+31 (Netherlands)',
    '+46 (Sweden)' => '+46 (Sweden)',
    '+47 (Norway)' => '+47 (Norway)',
    '+45 (Denmark)' => '+45 (Denmark)',
    '+358 (Finland)' => '+358 (Finland)',
    '+420 (Czech Republic)' => '+420 (Czech Republic)',
    '+421 (Slovakia)' => '+421 (Slovakia)',
    '+36 (Hungary)' => '+36 (Hungary)',
    '+40 (Romania)' => '+40 (Romania)',
    '+30 (Greece)' => '+30 (Greece)',
    '+351 (Portugal)' => '+351 (Portugal)',
    '+353 (Ireland)' => '+353 (Ireland)',
    '+32 (Belgium)' => '+32 (Belgium)',
    '+41 (Switzerland)' => '+41 (Switzerland)',
    '+43 (Austria)' => '+43 (Austria)',
    '+7 (Russia)' => '+7 (Russia)',
    '+380 (Ukraine)' => '+380 (Ukraine)',
    '+375 (Belarus)' => '+375 (Belarus)',
    '+86 (China)' => '+86 (China)',
    '+81 (Japan)' => '+81 (Japan)',
    '+82 (South Korea)' => '+82 (South Korea)',
    '+91 (India)' => '+91 (India)',
    '+61 (Australia)' => '+61 (Australia)',
    '+64 (New Zealand)' => '+64 (New Zealand)',
    '+55 (Brazil)' => '+55 (Brazil)',
    '+52 (Mexico)' => '+52 (Mexico)',
    '+54 (Argentina)' => '+54 (Argentina)',
    '+56 (Chile)' => '+56 (Chile)',
    '+57 (Colombia)' => '+57 (Colombia)',
    '+27 (South Africa)' => '+27 (South Africa)',
    '+20 (Egypt)' => '+20 (Egypt)',
    '+971 (United Arab Emirates)' => '+971 (United Arab Emirates)',
    '+966 (Saudi Arabia)' => '+966 (Saudi Arabia)',
    '+972 (Israel)' => '+972 (Israel)',
    '+90 (Turkey)' => '+90 (Turkey)',
    
    // PhoneNumberField translations
    'Country Code' => 'Country Code',
    'Select country code' => 'Select country code',
    'Phone Number' => 'Phone Number',
    'Enter phone number' => 'Enter phone number',
];
