<?php

return [
    // PhoneNumber translations
    'Phone number should not start with +. Use fromE164() for E.164 formatted numbers or provide prefix separately.' => 'Numer telefonu nie powinien zaczynać się od +. Użyj fromE164() dla numerów w formacie E.164 lub podaj prefiks oddzielnie.',
    'Phone number must contain only digits' => 'Numer telefonu może zawierać tylko cyfry',
    'E.164 phone number must start with +' => 'Numer telefonu w formacie E.164 musi zaczynać się od +',
    'Phone number with + prefix must be in E.164 format (e.g., +123456789)' => 'Numer telefonu z prefiksem + musi być w formacie E.164 (np. +123456789)',
    'Invalid E.164 format' => 'Nieprawidłowy format E.164',
    'Invalid phone number format. Without a country prefix, the first digit must be 1-9.' => 'Nieprawidłowy format numeru telefonu. Bez prefiksu krajowego pierwsza cyfra musi być z zakresu 1-9.',
    'Phone number cannot be empty' => 'Numer telefonu nie może być pusty',
    'Phone number must start with 1-9' => 'Numer telefonu musi zaczynać się od cyfry 1-9',
    
    // Country prefixes
    '+1 (United States & Canada)' => '+1 (Stany Zjednoczone i Kanada)',
    '+44 (United Kingdom)' => '+44 (Wielka Brytania)',
    '+48 (Poland)' => '+48 (Polska)',
    '+49 (Germany)' => '+49 (Niemcy)',
    '+33 (France)' => '+33 (Francja)',
    '+34 (Spain)' => '+34 (Hiszpania)',
    '+39 (Italy)' => '+39 (Włochy)',
    '+31 (Netherlands)' => '+31 (Holandia)',
    '+46 (Sweden)' => '+46 (Szwecja)',
    '+47 (Norway)' => '+47 (Norwegia)',
    '+45 (Denmark)' => '+45 (Dania)',
    '+358 (Finland)' => '+358 (Finlandia)',
    '+420 (Czech Republic)' => '+420 (Czechy)',
    '+421 (Slovakia)' => '+421 (Słowacja)',
    '+36 (Hungary)' => '+36 (Węgry)',
    '+40 (Romania)' => '+40 (Rumunia)',
    '+30 (Greece)' => '+30 (Grecja)',
    '+351 (Portugal)' => '+351 (Portugalia)',
    '+353 (Ireland)' => '+353 (Irlandia)',
    '+32 (Belgium)' => '+32 (Belgia)',
    '+41 (Switzerland)' => '+41 (Szwajcaria)',
    '+43 (Austria)' => '+43 (Austria)',
    '+7 (Russia)' => '+7 (Rosja)',
    '+380 (Ukraine)' => '+380 (Ukraina)',
    '+375 (Belarus)' => '+375 (Białoruś)',
    '+86 (China)' => '+86 (Chiny)',
    '+81 (Japan)' => '+81 (Japonia)',
    '+82 (South Korea)' => '+82 (Korea Południowa)',
    '+91 (India)' => '+91 (Indie)',
    '+61 (Australia)' => '+61 (Australia)',
    '+64 (New Zealand)' => '+64 (Nowa Zelandia)',
    '+55 (Brazil)' => '+55 (Brazylia)',
    '+52 (Mexico)' => '+52 (Meksyk)',
    '+54 (Argentina)' => '+54 (Argentyna)',
    '+56 (Chile)' => '+56 (Chile)',
    '+57 (Colombia)' => '+57 (Kolumbia)',
    '+27 (South Africa)' => '+27 (Republika Południowej Afryki)',
    '+20 (Egypt)' => '+20 (Egipt)',
    '+971 (United Arab Emirates)' => '+971 (Zjednoczone Emiraty Arabskie)',
    '+966 (Saudi Arabia)' => '+966 (Arabia Saudyjska)',
    '+972 (Israel)' => '+972 (Izrael)',
    '+90 (Turkey)' => '+90 (Turcja)',
    
    // PhoneNumberField translations
    'Country Code' => 'Kod kraju',
    'Select country code' => 'Wybierz kod kraju',
    'Phone Number' => 'Numer telefonu',
    'Enter phone number' => 'Wprowadź numer telefonu',
];
