@php
    use Filament\Support\Facades\FilamentView;

    $id = $getId();
    $statePath = $getStatePath();
    $isDisabled = $isDisabled();
    $actions = $getActions();
@endphp

<x-dynamic-component
    :component="$getFieldWrapperView()"
    :field="$field"
>
    <div
        x-data="{
            state: $wire.entangle('{{ $statePath }}'),
        }"
        id="{{ $id }}"
        {{ $attributes->merge($getExtraAttributes())->class(['filament-forms-phone-input-component']) }}
    >
        {{ $actions }}
    </div>
</x-dynamic-component>
