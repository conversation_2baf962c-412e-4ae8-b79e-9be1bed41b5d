APP_NAME="Salesto"
APP_ENV=local
APP_KEY=base64:8utuO5b86OClx3Wa0PhVH9QZ/U4hdsO6TLLht1rkS8U=
APP_DEBUG=true
APP_TIMEZONE=Europe/Warsaw
APP_URL=http://localhost
APP_PORT=81

DEBUGBAR_ENABLED=true

APP_LOCALE=pl
APP_FALLBACK_LOCALE=pl
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file

PHP_CLI_SERVER_WORKERS=4

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=pgsql
DB_HOST=postgres
DB_DATABASE=salesto_local
DB_USERNAME=salesto
DB_PASSWORD=salesto

SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync

CACHE_STORE=array
CACHE_PREFIX=

MEMCACHED_HOST=127.0.0.1

REDIS_CLIENT=phpredis
REDIS_HOST=redis
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=log
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

VITE_APP_NAME="${APP_NAME}"

ALLEGRO_CLIENT_ID=01847317248743d6949e5d36ffd0c1f9
ALLEGRO_CLIENT_SECRET=SBfCLZCZu0odigPtPcwoO1NWfCaCDQEEQabaefxkWJk6kFjwCl7yNQt8DJAHNo9l
ALLEGRO_API_URL=https://api.allegro.pl.allegrosandbox.pl/
ALLEGRO_OAUTH_TOKEN=https://allegro.pl.allegrosandbox.pl/auth/oauth/token
ALLEGRO_OAUTH_AUTHORIZE=https://allegro.pl.allegrosandbox.pl/auth/oauth/authorize

# Xdebug configuration
XDEBUG_MODE=debug
XDEBUG_CONFIG=client_host=host.docker.internal
