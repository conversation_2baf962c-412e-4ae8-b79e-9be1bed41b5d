{
    // Editor
    "editor.inlineSuggest.showToolbar": "never",
    "editor.inlineSuggest.enabled": false,

    // Copilot
    "github.copilot.enable": {
        "*": false
    },

    // Zencoder
    "zencoder.enableRepoIndexing": true,
    "zencoder.displayDebugInfo": true,
    "zencoder.codeCompletion.enableMultiLine": true,
    "zencoder.codeCompletion.enable": true,

    // PHP settings
    "php.validate.executablePath": "bin/php",
    "php.debug.executablePath": "bin/php",

    "php.suggest.basic": true,
    // IntelliSense settings
    "intelephense.environment.includePaths": [
        "vendor"
    ],
    "intelephense.files.maxSize": 5000000,
    // Better Pest settings
    "better-pest.docker.enable": true,
    "better-pest.docker.command": "bin/php \"{code}\"",
    "better-pest.docker.paths": {
        "/Users/<USER>/Projects/Private/salesto": "/var/www/html"
    },
    // PHPUnit
    "phpunit.php": "php",
    "phpunit.args": [
        "--colors=always",
    ],
    "phpunit.paths": {
        "${workspaceFolder}": "/var/www/html"
    },
    "phpunit.phpunit": "${workspaceFolder}/vendor/bin/pest",
    "phpunit.command": "docker compose exec php",
    "phpunit.debuggerConfig": "Listen for Xdebug",

    // PHP CS Fixer settings
    "php-cs-fixer.executablePath": "${workspaceFolder}/vendor/bin/php-cs-fixer",
    "php-cs-fixer.onsave": true,
    "php-cs-fixer.config": ".php-cs-fixer.php",
    // PHP Namespace Resolver settings
    "namespaceResolver.sortAlphabetically": true,
    "namespaceResolver.leadingSeparator": true,
    // Editor settings
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
        "source.fixAll": "explicit"
    },
    // Terminal settings
    "terminal.integrated.defaultProfile.linux": "bash",
    "terminal.integrated.profiles.linux": {
        "bash": {
            "path": "bash",
            "icon": "terminal-bash"
        }
    },


    "explorer.confirmDragAndDrop": false,
    "git.enableSmartCommit": true,
    "git.confirmSync": false,
    "github.copilot.chat.codesearch.enabled": true,
    "github.copilot.chat.scopeSelection": true,

    // Augment
    "augment.nextEdit.enableBackgroundSuggestions": false,
    "augment.completions.enableQuickSuggestions": false,
    "augment.completions.enableAutomaticCompletions": false,
    "augment.enableEmptyFileHint": false,
    "augment.chat.userGuidelines": ".augment-guidelines",

    // Laravel settings
    //"Laravel.phpCommand": "bin/exec \"{code}\"",
    "Laravel.phpCommand": "bin/php \"{code}\"",
    "artisan.docker.enabled": true,
    "artisan.docker.command": "bin/php",

    // Continue
    "continue.enableTabAutocomplete": false,

    // PHP-CS-Fixer
    "php-cs-fixer.executablePathWindows": "bin/php-cs-fixer",
    "zencoder.mcpServers": {
        "Context7": {
            "command": "npx",
            "args": [
                "-y",
                "@upstash/context7-mcp@latest"
            ]
        },
        "OpenMemory": {
            "command": "npx",
            "args": [
                "mcp-remote",
                "http://localhost:8765/mcp/openmemory/sse/patryktrochowski"
            ]
        }
    }
}
