{"version": "2.0.0", "tasks": [{"label": "Run Current Pest Test File", "type": "shell", "command": "task tests:file -- '${relativeFile} -v'", "group": "test", "presentation": {"reveal": "always", "panel": "new", "focus": true}, "problemMatcher": []}, {"label": "Run All Pest Tests", "type": "shell", "command": "task tests:all", "group": "test", "presentation": {"reveal": "always", "panel": "new", "focus": true}, "problemMatcher": []}, {"label": "Run Current Feature Test", "type": "shell", "command": "task tests:file -- '${relativeFile} --group=feature -v'", "group": "test", "presentation": {"reveal": "always", "panel": "new", "focus": true}, "problemMatcher": []}, {"label": "Run Current Unit Test", "type": "shell", "command": "task tests:file -- '${relativeFile} --group=unit -v'", "group": "test", "presentation": {"reveal": "always", "panel": "new", "focus": true}, "problemMatcher": []}, {"label": "Run PHP CS Fixer", "type": "shell", "command": "task exec -- 'vendor/bin/php-cs-fixer fix --config=.php-cs-fixer.php'", "group": "none", "presentation": {"reveal": "always", "panel": "new"}, "problemMatcher": []}, {"label": "Run PHP CS Fixer on Current File", "type": "shell", "command": "task exec -- 'vendor/bin/php-cs-fixer fix --config=.php-cs-fixer.php ${relativeFile}'", "group": "none", "presentation": {"reveal": "always", "panel": "new"}, "problemMatcher": []}, {"label": "Run Artisan Command", "type": "shell", "command": "task artisan -- ${input:artisanCommand}", "group": "none", "presentation": {"reveal": "always", "panel": "new"}, "problemMatcher": []}, {"label": "Run Composer Command", "type": "shell", "command": "task composer -- ${input:composer<PERSON>ommand}", "group": "none", "presentation": {"reveal": "always", "panel": "new"}, "problemMatcher": []}, {"label": "Open Shell in Container", "type": "shell", "command": "task sh", "group": "none", "presentation": {"reveal": "always", "panel": "new"}, "problemMatcher": []}, {"label": "Toggle Xdebug", "type": "shell", "command": "task xdebug:toggle", "group": "none", "presentation": {"reveal": "always", "panel": "new", "focus": true}, "problemMatcher": []}, {"label": "Enable Xdebug", "type": "shell", "command": "task xdebug:on", "group": "none", "presentation": {"reveal": "always", "panel": "new", "focus": true}, "problemMatcher": []}, {"label": "Disable Xdebug", "type": "shell", "command": "task xdebug:off", "group": "none", "presentation": {"reveal": "always", "panel": "new", "focus": true}, "problemMatcher": []}, {"label": "Run PHP Script", "type": "shell", "command": "task php:run -- ${file}", "group": "none", "presentation": {"reveal": "always", "panel": "new", "focus": true}, "problemMatcher": []}, {"label": "Debug PHP Script", "type": "shell", "command": "task php:debug -- ${file}", "group": "none", "presentation": {"reveal": "always", "panel": "new", "focus": true}, "problemMatcher": []}, {"label": "Debug Current Test File", "type": "shell", "command": "task tests:debug:file -- '${relativeFile} -v'", "group": "test", "presentation": {"reveal": "always", "panel": "new", "focus": true}, "problemMatcher": []}, {"label": "Debug All Tests", "type": "shell", "command": "task tests:debug", "group": "test", "presentation": {"reveal": "always", "panel": "new", "focus": true}, "problemMatcher": []}, {"label": "Run Specific Test Method", "type": "shell", "command": "task tests:filter -- ${input:testMethod}", "group": "test", "presentation": {"reveal": "always", "panel": "new", "focus": true}, "problemMatcher": []}, {"label": "Debug Specific Test Method", "type": "shell", "command": "task tests:debug:filter -- ${input:testMethod}", "group": "test", "presentation": {"reveal": "always", "panel": "new", "focus": true}, "problemMatcher": []}, {"label": "Run Allegro OAuth Server", "type": "shell", "command": "task oauth:server", "group": "none", "presentation": {"reveal": "always", "panel": "new", "focus": true}, "problemMatcher": []}, {"label": "<PERSON>", "type": "shell", "command": "task up", "group": "none", "presentation": {"reveal": "always", "panel": "new", "focus": true}, "problemMatcher": []}], "inputs": [{"id": "artisanCommand", "description": "Artisan command to run", "default": "", "type": "promptString"}, {"id": "<PERSON><PERSON><PERSON><PERSON>", "description": "Composer command to run", "default": "", "type": "promptString"}, {"id": "testMethod", "description": "Test method to run (e.g. 'TestClass::testMethod' or just 'testMethod')", "default": "", "type": "promptString"}]}