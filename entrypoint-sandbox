#!/bin/bash
set -e

# =============================================================================
# Sandbox Container Entrypoint - Clean Implementation
# =============================================================================

# Environment variables
SANDBOX_USER=${SANDBOX_USER:-allhands}
SANDBOX_AUTO_SETUP=${SANDBOX_AUTO_SETUP:-true}
export COREPACK_ENABLE_DOWNLOAD_PROMPT=0

# =============================================================================
# Docker Setup
# =============================================================================

setup_docker() {
    if [ -S /var/run/docker.sock ]; then
        if docker info >/dev/null 2>&1; then
            echo "Using existing Docker daemon (host socket)"
        else
            echo "Fixing Docker socket permissions..."
            chown root:docker /var/run/docker.sock 2>/dev/null || true
            chmod 660 /var/run/docker.sock 2>/dev/null || true
            if docker info >/dev/null 2>&1; then
                echo "Docker daemon is now accessible!"
            else
                echo "ERROR: Docker socket exists but daemon is not accessible"
                exit 1
            fi
        fi
    else
        echo "No Docker socket found, starting Docker daemon inside container..."
        start_docker_daemon
    fi
}

start_docker_daemon() {
    echo "Starting Docker daemon..."

    # Create necessary directories
    mkdir -p /var/lib/docker /var/run

    # Start dockerd with better error handling
    dockerd \
        --host=unix:///var/run/docker.sock \
        --host=tcp://0.0.0.0:2375 \
        --storage-driver=overlay2 \
        --iptables=false \
        --ip-masq=false \
        --dns=******* \
        --dns=******* \
        --raw-logs \
        --log-level=info \
        >/var/log/docker.log 2>&1 &

    echo "Waiting for Docker daemon to start..."
    for i in {1..30}; do
        if docker info >/dev/null 2>&1; then
            echo "Docker daemon is ready!"
            return 0
        fi
        echo "Waiting... ($i/30)"
        sleep 2
    done

    echo "ERROR: Docker daemon failed to start"
    echo "Docker daemon logs:"
    tail -20 /var/log/docker.log 2>/dev/null || echo "No Docker log available"
    echo "Checking Docker socket:"
    ls -la /var/run/docker.sock 2>/dev/null || echo "Docker socket not found"
    exit 1
}

# =============================================================================
# System Verification
# =============================================================================

verify_system() {
    echo "Verifying Python installation..."
    python3 --version
    pip3 --version

    echo "Verifying Node.js installation..."
    node --version
    npm --version
    yarn --version

    if command -v corepack >/dev/null 2>&1; then
        corepack --version
    else
        echo "corepack not available (optional)"
    fi
}

# =============================================================================
# Command Execution
# =============================================================================

run_setup() {
    if [ "$SANDBOX_AUTO_SETUP" = "true" ] && [ -f ./setup.sh ]; then
        echo "Running setup script automatically..."
        ./setup.sh || echo "WARNING: Setup failed, continuing..."
    fi
}

execute_command() {
    cd /workspace

    if [ "$#" -eq 0 ]; then
        # No command - run setup and start shell
        run_setup
        # Start bash in a way that avoids job control issues in Docker
        # Use exec with proper environment and shell options
        export PS1='\u@\h:\w\$ '
        exec /bin/bash --norc --noprofile
    else
        # Execute provided command
        exec "$@"
    fi
}

# =============================================================================
# Main
# =============================================================================

main() {
    setup_docker
    verify_system
    execute_command "$@"
}

main "$@"
