#!/bin/sh
set -ex

echo "Starting production entrypoint..."

# In production, dependencies should already be installed during build
# But we can add a safety check
if [ ! -d "vendor" ]; then
    echo "ERROR: Vendor directory not found in production build"
    exit 1
fi

echo "Dependencies verified"

# Run the original entrypoint logic
echo "Running Laravel setup commands..."

# first arg is `-f` or `--some-option`
if [ "${1#-}" != "$1" ]; then
	set -- php-fpm "$@"
fi

if [ "$1" = 'php-fpm' ]; then
    echo "Setting up Laravel application..."
    php artisan config:cache -v
    php artisan route:cache -v
    php artisan view:cache -v
    php artisan filament:optimize-clear
    php artisan optimize -v
    php artisan filament:optimize -v
    php artisan migrate --force
    echo "Laravel setup completed"
fi

echo "Starting PHP-FPM..."
exec "$@"