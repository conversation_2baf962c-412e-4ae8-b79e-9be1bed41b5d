<?php

declare(strict_types=1);

namespace Product\UI\Filament\ProductResources\Pages;

use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Product\UI\Filament\ProductResource;

class ViewProduct extends ViewRecord
{
    protected static string $resource = ProductResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }
}
