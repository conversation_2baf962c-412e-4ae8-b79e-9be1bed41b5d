<?php

declare(strict_types=1);

namespace Product\UI\Filament\ProductResources\Pages;

use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Product\UI\Filament\ProductResource;

class ListProducts extends ListRecords
{
    protected static string $resource = ProductResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
