<?php

declare(strict_types=1);

namespace Product\UI\Filament\ProductResources\Pages;

use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Product\UI\Filament\ProductResource;

class EditProduct extends EditRecord
{
    protected static string $resource = ProductResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
            Actions\ForceDeleteAction::make(),
            Actions\RestoreAction::make(),
        ];
    }
}
