<?php

namespace Product\UI\Filament\DescriptionResource\Pages;

use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Product\UI\Filament\DescriptionResource;

class EditDescription extends EditRecord
{
    protected static string $resource = DescriptionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
            Actions\ForceDeleteAction::make(),
            Actions\RestoreAction::make(),
        ];
    }
}
