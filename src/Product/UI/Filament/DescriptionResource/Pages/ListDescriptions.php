<?php

namespace Product\UI\Filament\DescriptionResource\Pages;

use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Product\UI\Filament\DescriptionResource;

class ListDescriptions extends ListRecords
{
    protected static string $resource = DescriptionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
