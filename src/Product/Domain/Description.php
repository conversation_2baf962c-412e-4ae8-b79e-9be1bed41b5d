<?php

declare(strict_types=1);

namespace Product\Domain;

use Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * 
 *
 * @property-read Product|null $product
 * @method static Builder<static>|Description newModelQuery()
 * @method static Builder<static>|Description newQuery()
 * @method static Builder<static>|Description onlyTrashed()
 * @method static Builder<static>|Description query()
 * @method static Builder<static>|Description withTrashed()
 * @method static Builder<static>|Description withoutTrashed()
 * @mixin Eloquent
 */
class Description extends Model
{
    use SoftDeletes, HasUuids;

    protected $table = 'product.descriptions';

    protected $fillable = [
        'title',
        'content',
        'marketplace_id',
    ];

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }
}
