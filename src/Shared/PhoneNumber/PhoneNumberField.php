<?php

declare(strict_types=1);

namespace Shared\PhoneNumber;

use Filament\Forms\Components\Field;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Grid;
use Filament\Forms\Get;
use Filament\Forms\Set;

class PhoneNumberField extends Field
{
    protected string $view = 'filament-forms::components.grid';

    public function setUp(): void
    {
        parent::setUp();

        $this->columnSpan(2);

        $this->childComponents([
            Grid::make(2)
                ->schema([
                    Select::make($this->getStatePath() . '_prefix')
                        ->label(__d('phone-number', 'Kod kraju'))
                        ->options(PhoneNumber::getCommonPrefixes())
                        ->searchable()
                        ->placeholder(__d('phone-number', 'Wybierz kod kraju'))
                        ->afterStateUpdated(function (Set $set, ?string $state) {
                            $this->updatePhoneNumberState($set, $state, null);
                        })
                        ->columnSpan(1),

                    TextInput::make($this->getStatePath() . '_number')
                        ->label(__d('phone-number', 'Numer telefonu'))
                        ->tel()
                        ->placeholder(__d('phone-number', 'Wprowadź numer telefonu'))
                        ->afterStateUpdated(function (Set $set, ?string $state, Get $get) {
                            $prefix = $get($this->getStatePath() . '_prefix');
                            $this->updatePhoneNumberState($set, $prefix, $state);
                        })
                        ->columnSpan(1),
                ])
                ->columnSpan(2),
        ]);

        $this->afterStateHydrated(function (Set $set, $state) {
            if (empty($state)) {
                $set($this->getStatePath() . '_prefix', null);
                $set($this->getStatePath() . '_number', null);
                return;
            }

            try {
                $phoneNumber = PhoneNumber::fromString($state);
                $set($this->getStatePath() . '_prefix', $phoneNumber->getPrefix());
                $set($this->getStatePath() . '_number', $phoneNumber->getNumber());
            } catch (\InvalidArgumentException) {
                // If the phone number is invalid, just set the raw value as the number
                $set($this->getStatePath() . '_prefix', null);
                $set($this->getStatePath() . '_number', $state);
            }
        });

        $this->dehydrateStateUsing(function (Get $get) {
            // We get components directly from the form state
            $prefix = $get($this->getStatePath() . '_prefix');
            $number = $get($this->getStatePath() . '_number');

            if (empty($number)) {
                return null;
            }

            try {
                $phoneNumber = new PhoneNumber($number, $prefix);
                return $phoneNumber->getValue();
            } catch (\InvalidArgumentException) {
                return $number;
            }
        });
    }

    /**
     * Update the phone number state
     *
     * @param Set $set
     * @param string|null $prefix
     * @param string|null $number
     * @return void
     */
    protected function updatePhoneNumberState(Set $set, ?string $prefix, ?string $number): void
    {
        if (empty($number)) {
            $set($this->getStatePath(), null);
            return;
        }

        try {
            $phoneNumber = new PhoneNumber($number ?? '', $prefix);
            $set($this->getStatePath(), $phoneNumber->getValue());
        } catch (\InvalidArgumentException) {
            $set($this->getStatePath(), $number);
        }
    }

    /**
     * Create a new PhoneNumberField instance
     *
     * @param string $name
     * @return static
     */
    public static function make(string $name): static
    {
        return app(static::class, ['name' => $name]);
    }
}
