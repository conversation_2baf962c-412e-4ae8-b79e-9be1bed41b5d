<?php

declare(strict_types=1);

namespace Shared\PhoneNumber;

use InvalidArgumentException;

class PhoneNumber
{
    private ?string $prefix;
    private string $number;

    /**
     * Create a new PhoneNumber instance
     *
     * @param string $number Phone number without + prefix
     * @param string|null $prefix Optional country code prefix (without +)
     * @throws InvalidArgumentException If the phone number is not valid
     */
    public function __construct(string $number, ?string $prefix = null)
    {
        // Don't allow numbers with + in the constructor
        if (str_starts_with($number, '+')) {
            throw new InvalidArgumentException('Phone number should not start with +. Use fromE164() for E.164 formatted numbers or provide prefix separately.');
        }

        // Check for non-digit characters before cleaning
        if (preg_match('/[^\d]/', $number)) {
            throw new InvalidArgumentException('Phone number must contain only digits');
        }

        $this->number = $this->cleanNumber($number);
        $this->prefix = $prefix;

        // Validate the number - only validate first digit if no prefix is provided
        if ($this->prefix === null) {
            $this->validateNumber($this->number);
        }
    }

    /**
     * Create a PhoneNumber from an E.164 formatted string
     *
     * @param string $e164Number Phone number in E.164 format (with + prefix)
     * @return self
     * @throws InvalidArgumentException If the phone number is not in valid E.164 format
     */
    public static function fromE164(string $e164Number): self
    {
        if (!str_starts_with($e164Number, '+')) {
            throw new InvalidArgumentException('E.164 phone number must start with +');
        }

        if (!self::isValidE164($e164Number)) {
            throw new InvalidArgumentException('Phone number with + prefix must be in E.164 format (e.g., +123456789)');
        }

        // Extract prefix and number from E.164 format
        // Remove the + sign
        $numberWithoutPlus = substr($e164Number, 1);

        // Extract the prefix and number based on common country codes
        $prefixes = array_keys(self::getCommonPrefixes());

        // Sort prefixes by length (descending) to match longer prefixes first
        usort($prefixes, function ($a, $b) {
            return strlen((string)$b) <=> strlen((string)$a);
        });

        foreach ($prefixes as $prefix) {
            $prefixStr = (string)$prefix;
            if (str_starts_with($numberWithoutPlus, $prefixStr)) {
                $number = substr($numberWithoutPlus, strlen($prefixStr));
                return new self($number, $prefixStr);
            }
        }

        // If no specific prefix is matched, use a simple approach
        // Default to treating the first 1-3 digits as the prefix
        if (preg_match('/^(\d{1,3})(\d+)$/', $numberWithoutPlus, $matches)) {
            $prefix = $matches[1];
            $number = $matches[2];

            return new self($number, $prefix);
        }

        throw new InvalidArgumentException('Invalid E.164 format');
    }

    /**
     * Create a PhoneNumber from a raw string
     *
     * @param string|null $phoneNumber Raw phone number
     * @param string $defaultPrefix Default country prefix to add if the number doesn't start with +
     * @return PhoneNumber|null
     * @throws InvalidArgumentException If the resulting phone number is not valid
     */
    public static function fromString(?string $phoneNumber, string $defaultPrefix = ''): ?self
    {
        if (empty($phoneNumber)) {
            return null;
        }

        // Remove any non-digit characters except the leading +
        $cleaned = preg_replace('/[^\d+]/', '', $phoneNumber);

        // If it starts with +, use the fromE164 method
        if (str_starts_with($cleaned, '+')) {
            return self::fromE164($cleaned);
        }

        // If a default prefix is provided, use it
        if (!empty($defaultPrefix)) {
            return new self($cleaned, $defaultPrefix);
        }

        // If no prefix is provided, validate the number
        if (!preg_match('/^[1-9]/', $cleaned)) {
            throw new InvalidArgumentException('Invalid phone number format. Without a country prefix, the first digit must be 1-9.');
        }

        // Create a phone number without prefix
        return new self($cleaned);
    }

    /**
     * Clean a phone number by removing non-digit characters
     *
     * @param string $number The number to clean
     * @return string
     */
    private function cleanNumber(string $number): string
    {
        return preg_replace('/[^\d]/', '', $number);
    }

    /**
     * Validate a phone number
     *
     * @param string $number The number to validate
     * @throws InvalidArgumentException If the number is invalid
     */
    private function validateNumber(string $number): void
    {
        if (empty($number)) {
            throw new InvalidArgumentException('Phone number cannot be empty');
        }

        if (!preg_match('/^[0-9]+$/', $number)) {
            throw new InvalidArgumentException('Phone number must contain only digits');
        }

        if (!preg_match('/^[1-9]/', $number)) {
            throw new InvalidArgumentException('Phone number must start with 1-9');
        }
    }

    /**
     * Check if a string is in E.164 format
     *
     * @param string $phoneNumber Phone number to check
     * @return bool
     */
    private static function isValidE164(string $phoneNumber): bool
    {
        // E.164 format: + followed by 1-15 digits
        // The first digit after + must be 1-9 (not 0)
        return preg_match('/^\+[1-9]\d{1,14}$/', $phoneNumber) === 1;
    }

    /**
     * Get the phone number as a string
     *
     * @return string
     */
    public function __toString(): string
    {
        return $this->getValue();
    }

    /**
     * Get the phone number value
     *
     * @return string
     */
    public function getValue(): string
    {
        if ($this->prefix) {
            return '+' . $this->prefix . $this->number;
        }

        return $this->number;
    }

    /**
     * Get the country code prefix (without +)
     *
     * @return string|null
     */
    public function getPrefix(): ?string
    {
        return $this->prefix;
    }

    /**
     * Get the phone number without the prefix
     *
     * @return string
     */
    public function getNumber(): string
    {
        return $this->number;
    }

    /**
     * Format the phone number for display
     *
     * @return string
     */
    public function formatForDisplay(): string
    {
        // If no prefix, format based on number length
        if (!$this->prefix) {
            $length = strlen($this->number);

            if ($length === 9) {
                // Format as XXX XXX XXX (common for many countries)
                return preg_replace('/^(\d{3})(\d{3})(\d{3})$/', '$1 $2 $3', $this->number);
            } elseif ($length === 10) {
                // Format as XXX XXX XXXX (common for US/Canada)
                return preg_replace('/^(\d{3})(\d{3})(\d{4})$/', '$1 $2 $3', $this->number);
            } else {
                // For other lengths, add spaces every 3 digits
                return implode(' ', str_split($this->number, 3));
            }
        }

        // Format with prefix
        if (strlen($this->number) === 9) {
            // Format as +XX XXX XXX XXX (common for many countries including Poland)
            return sprintf(
                '+%s %s %s %s',
                $this->prefix,
                substr($this->number, 0, 3),
                substr($this->number, 3, 3),
                substr($this->number, 6, 3)
            );
        } elseif (strlen($this->number) === 10) {
            // Format as +XX XXX XXX XXXX (common for US/Canada)
            return sprintf(
                '+%s %s %s %s',
                $this->prefix,
                substr($this->number, 0, 3),
                substr($this->number, 3, 3),
                substr($this->number, 6, 4)
            );
        }

        // Default formatting - just add a space after the country code
        return "+{$this->prefix} {$this->number}";
    }

    /**
     * Get common country prefixes for phone numbers
     *
     * @return array<string, string>
     */
    public static function getCommonPrefixes(): array
    {
        return [
            '1' => __d('phone-number', '+1 (Stany Zjednoczone i Kanada)'),
            '44' => __d('phone-number', '+44 (Wielka Brytania)'),
            '48' => __d('phone-number', '+48 (Polska)'),
            '49' => __d('phone-number', '+49 (Niemcy)'),
            '33' => __d('phone-number', '+33 (Francja)'),
            '34' => __d('phone-number', '+34 (Hiszpania)'),
            '39' => __d('phone-number', '+39 (Włochy)'),
            '31' => __d('phone-number', '+31 (Holandia)'),
            '46' => __d('phone-number', '+46 (Szwecja)'),
            '47' => __d('phone-number', '+47 (Norwegia)'),
            '45' => __d('phone-number', '+45 (Dania)'),
            '358' => __d('phone-number', '+358 (Finlandia)'),
            '420' => __d('phone-number', '+420 (Czechy)'),
            '421' => __d('phone-number', '+421 (Słowacja)'),
            '36' => __d('phone-number', '+36 (Węgry)'),
            '40' => __d('phone-number', '+40 (Rumunia)'),
            '30' => __d('phone-number', '+30 (Grecja)'),
            '351' => __d('phone-number', '+351 (Portugalia)'),
            '353' => __d('phone-number', '+353 (Irlandia)'),
            '32' => __d('phone-number', '+32 (Belgia)'),
            '41' => __d('phone-number', '+41 (Szwajcaria)'),
            '43' => __d('phone-number', '+43 (Austria)'),
            '7' => __d('phone-number', '+7 (Rosja)'),
            '380' => __d('phone-number', '+380 (Ukraina)'),
            '375' => __d('phone-number', '+375 (Białoruś)'),
            '86' => __d('phone-number', '+86 (Chiny)'),
            '81' => __d('phone-number', '+81 (Japonia)'),
            '82' => __d('phone-number', '+82 (Korea Południowa)'),
            '91' => __d('phone-number', '+91 (Indie)'),
            '61' => __d('phone-number', '+61 (Australia)'),
            '64' => __d('phone-number', '+64 (Nowa Zelandia)'),
            '55' => __d('phone-number', '+55 (Brazylia)'),
            '52' => __d('phone-number', '+52 (Meksyk)'),
            '54' => __d('phone-number', '+54 (Argentyna)'),
            '56' => __d('phone-number', '+56 (Chile)'),
            '57' => __d('phone-number', '+57 (Kolumbia)'),
            '27' => __d('phone-number', '+27 (Republika Południowej Afryki)'),
            '20' => __d('phone-number', '+20 (Egipt)'),
            '971' => __d('phone-number', '+971 (Zjednoczone Emiraty Arabskie)'),
            '966' => __d('phone-number', '+966 (Arabia Saudyjska)'),
            '972' => __d('phone-number', '+972 (Izrael)'),
            '90' => __d('phone-number', '+90 (Turcja)'),
        ];
    }
}
