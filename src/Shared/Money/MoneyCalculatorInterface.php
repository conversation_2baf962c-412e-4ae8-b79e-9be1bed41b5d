<?php

declare(strict_types=1);

namespace Shared\Money;

/**
 * Interface for money calculation operations
 */
interface MoneyCalculatorInterface
{
    /**
     * Add two numbers with the specified scale
     *
     * @param string $a First number
     * @param string $b Second number
     * @param int $scale Scale
     * @return string
     */
    public static function add(string $a, string $b, int $scale = 2): string;

    /**
     * Subtract two numbers with the specified scale
     *
     * @param string $a First number
     * @param string $b Second number
     * @param int $scale Scale
     * @return string
     */
    public static function subtract(string $a, string $b, int $scale = 2): string;

    /**
     * Multiply two numbers with the specified scale
     *
     * @param string $a First number
     * @param string $b Second number
     * @param int $scale Scale
     * @return string
     */
    public static function multiply(string $a, string $b, int $scale = 2): string;

    /**
     * Divide two numbers with the specified scale
     *
     * @param string $a First number
     * @param string $b Second number
     * @param int $scale Scale
     * @return string
     */
    public static function divide(string $a, string $b, int $scale = 2): string;

    /**
     * Round a number to the specified scale
     *
     * @param string $number The number to round
     * @param int $precision The scale to round to
     * @param int $mode Rounding mode (PHP_ROUND_HALF_UP by default)
     * @return string
     */
    public static function round(string $number, int $precision = 2, int $mode = PHP_ROUND_HALF_UP): string;

    /**
     * Sum multiple numbers and round the result
     *
     * @param array $numbers The numbers to sum
     * @param int $precision The precision to use
     * @param int $roundMode The rounding mode to use
     * @return string
     */
    public static function sumAndRound(
        array $numbers,
        int $precision = 2,
        int $roundMode = PHP_ROUND_HALF_UP
    ): string;

    /**
     * Calculate a percentage of a value
     *
     * @param string $value The value to calculate the percentage of
     * @param string $percentage The percentage to calculate
     * @param int $precision The precision to use
     * @return string
     */
    public static function percentageOf(string $value, string $percentage = '100', int $precision = 2): string;

    /**
     * Compare two numbers
     *
     * @param string $a First number
     * @param string $b Second number
     * @param int $scale Scale
     * @return int Returns 0 if the two operands are equal, 1 if the left operand is larger, -1 otherwise
     */
    public static function compare(string $a, string $b, int $scale = 10): int;

    /**
     * Normalize the amount to the specified scale
     *
     * @param string $amount The amount to normalize
     * @param int $scale The scale to normalize to
     * @return string
     */
    public static function normalizeAmount(string $amount, int $scale = 2): string;

    /**
     * Check if the amount is valid (numeric and not negative)
     *
     * @param string $amount The amount to check
     * @return bool
     */
    public static function isValidAmount(string $amount): bool;
}
