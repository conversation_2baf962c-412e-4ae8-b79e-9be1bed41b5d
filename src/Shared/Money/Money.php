<?php

declare(strict_types=1);

namespace Shared\Money;

use InvalidArgumentException;
use JsonSerializable;

class Money implements JsonSerializable
{
    private string $amount;
    private int $scale = 2;
    private MoneyCalculatorInterface $calculator;

    /**
     * Create a new Money instance
     *
     * @param string|int|float $amount The amount as a string, integer, or float
     * @param int $scale The number of decimal places (default: 2)
     * @throws InvalidArgumentException If the amount is negative
     */
    public function __construct($amount, int $scale = 2, ?MoneyCalculatorInterface $calculator = null)
    {
        $this->calculator = $calculator ?? new MoneyCalculator();

        // Convert amount to string if it's not already
        if (is_float($amount) || is_int($amount)) {
            $amount = (string)$amount;
        }

        // Validate amount
        if (!is_string($amount) || !$this->calculator->isValidAmount($amount)) {
            throw new InvalidArgumentException('Amount must be a valid numeric string');
        }

        $this->scale = $scale;

        // Normalize the amount to the specified scale
        $this->amount = $this->calculator->normalizeAmount($amount, $this->scale);
    }

    /**
     * Create a Money object from a raw value
     *
     * @param string|int|float|null $amount The amount
     * @return self|null
     */
    public static function fromRaw($amount = null, ?MoneyCalculatorInterface $calculator = null): ?self
    {
        if ($amount === null) {
            return null;
        }

        return new self($amount, 2, $calculator);
    }

    /**
     * Create a Money object or return the existing one
     *
     * @param string|Money $amount The amount as string or Money object
     * @param int $scale The number of decimal places (default: 2)
     * @return self
     */
    public static function create($amount, int $scale = 2, ?MoneyCalculatorInterface $calculator = null): self
    {
        if ($amount instanceof self) {
            return $amount;
        }

        return new self($amount, $scale, $calculator);
    }

    /**
     * Get the amount as a string
     *
     * @return string
     */
    public function getAmount(): string
    {
        return $this->amount;
    }

    /**
     * Get the amount as a float (for backward compatibility)
     *
     * @return float
     */
    public function getAmountAsFloat(): float
    {
        return (float)$this->amount;
    }

    /**
     * Format the money amount for display
     *
     * @return string
     */
    public function format(): string
    {
        // Format with number_format for proper thousands separators
        return number_format((float)$this->amount, $this->scale);
    }

    /**
     * Add another Money object to this one
     *
     * @param Money $money The Money object to add
     * @return self
     */
    public function add(Money $money): self
    {
        $result = $this->calculator->add($this->amount, $money->getAmount(), $this->scale);
        return new self($result, $this->scale);
    }

    /**
     * Subtract another Money object from this one
     *
     * @param Money $money The Money object to subtract
     * @return self
     * @throws InvalidArgumentException If the result would be negative
     */
    public function subtract(Money $money): self
    {
        $result = $this->calculator->subtract($this->amount, $money->getAmount(), $this->scale);

        if ($this->calculator->compare($result, '0', $this->scale) < 0) {
            throw new InvalidArgumentException('Result cannot be negative');
        }

        return new self($result, $this->scale);
    }

    /**
     * Multiply this Money object by a factor
     *
     * @param string|int|float $factor The factor to multiply by
     * @return self
     * @throws InvalidArgumentException If the factor is negative
     */
    public function multiply($factor): self
    {
        // Convert factor to string if it's not already
        if (is_float($factor) || is_int($factor)) {
            $factor = (string)$factor;
        }

        // Validate factor
        if (!is_string($factor) || !is_numeric($factor)) {
            throw new InvalidArgumentException('Factor must be a valid numeric string, integer, or float');
        }

        if ($this->calculator->compare($factor, '0', 10) < 0) {
            throw new InvalidArgumentException('Factor cannot be negative');
        }

        $result = $this->calculator->multiply($this->amount, $factor, $this->scale);
        return new self($result, $this->scale);
    }

    /**
     * Divide this Money object by a divisor
     *
     * @param string|int|float $divisor The divisor to divide by
     * @return self
     * @throws InvalidArgumentException If the divisor is zero or negative
     */
    public function divide($divisor): self
    {
        // Convert divisor to string if it's not already
        if (is_float($divisor) || is_int($divisor)) {
            $divisor = (string)$divisor;
        }

        // Validate divisor
        if (!is_string($divisor) || !is_numeric($divisor)) {
            throw new InvalidArgumentException('Divisor must be a valid numeric string, integer, or float');
        }

        if ($this->calculator->compare($divisor, '0', 10) <= 0) {
            throw new InvalidArgumentException('Divisor cannot be zero or negative');
        }

        $result = $this->calculator->divide($this->amount, $divisor, $this->scale);
        return new self($result, $this->scale);
    }

    /**
     * Check if this Money object equals another
     *
     * @param Money $money The Money object to compare with
     * @return bool
     */
    public function equals(Money $money): bool
    {
        return $this->calculator->compare($this->amount, $money->getAmount(), $this->scale) === 0;
    }

    /**
     * Convert this Money object to a string
     *
     * @return string
     */
    public function __toString(): string
    {
        return $this->amount;
    }

    /**
     * Serialize this Money object to JSON
     *
     * @return string
     */
    public function jsonSerialize(): string
    {
        return $this->amount;
    }
}
