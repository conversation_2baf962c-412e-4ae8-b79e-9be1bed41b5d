<?php

declare(strict_types=1);

namespace Shared\Money;

use InvalidArgumentException;
use JsonSerializable;

class Currency implements JsonSerializable
{
    private string $code;

    /**
     * Valid currency codes
     */
    private const VALID_CURRENCIES = ['PLN', 'EUR', 'USD', 'GBP'];

    /**
     * Currency symbols mapping
     */
    private const CURRENCY_SYMBOLS = [
        'PLN' => 'zł',
        'EUR' => '€',
        'USD' => '$',
        'GBP' => '£',
    ];

    /**
     * Create a new Currency instance
     *
     * @param string $code The currency code
     * @throws InvalidArgumentException If the currency code is invalid
     */
    public function __construct(string $code)
    {
        $code = strtoupper($code);

        if (!self::isValid($code)) {
            throw new InvalidArgumentException('Invalid currency code');
        }

        $this->code = $code;
    }

    /**
     * Check if the currency code is valid
     *
     * @param string $code The currency code to check
     * @return bool
     */
    public static function isValid(string $code): bool
    {
        return in_array(strtoupper($code), self::VALID_CURRENCIES);
    }

    /**
     * Get the currency code
     *
     * @return string
     */
    public function getCode(): string
    {
        return $this->code;
    }

    /**
     * Get the currency symbol
     *
     * @return string
     */
    public function getSymbol(): string
    {
        return self::CURRENCY_SYMBOLS[$this->code] ?? $this->code;
    }

    /**
     * Check if this Currency equals another
     *
     * @param Currency $currency The Currency to compare with
     * @return bool
     */
    public function equals(Currency $currency): bool
    {
        return $this->code === $currency->getCode();
    }

    /**
     * Convert this Currency to a string
     *
     * @return string
     */
    public function __toString(): string
    {
        return $this->code;
    }

    /**
     * Serialize this Currency to JSON
     *
     * @return string
     */
    public function jsonSerialize(): string
    {
        return $this->code;
    }
}
