<?php

declare(strict_types=1);

namespace Shared\Integration\Allegro\Adapter;

use Guz<PERSON>Http\Client as GuzzleClient;
use GuzzleHttp\ClientInterface;
use GuzzleHttp\Exception\GuzzleException;
use GuzzleHttp\RequestOptions;
use Psr\Http\Message\ResponseInterface;
use Psr\Log\LoggerInterface;
use Shared\Integration\Abstract\Port\Exception\AllegroException;
use Shared\Integration\Allegro\Port\AllegroAuthorizationData;

class AllegroClient
{
    private readonly ClientInterface $httpClient;

    public function __construct(
        private readonly AllegroAuthorizationData $authData,
        private readonly LoggerInterface $logger,
        private readonly string $baseUrl,
        ?ClientInterface $httpClient = null
    ) {
        $this->httpClient = $httpClient ?? new GuzzleClient([
            'base_uri' => $baseUrl,
        ]);
    }

    /**
     * Send a GET request to Allegro API
     *
     * @param string $endpoint API endpoint
     * @param array $queryParams Query parameters
     * @return ResponseInterface
     * @throws GuzzleException
     */
    public function get(string $endpoint, array $queryParams = []): ResponseInterface
    {
        return $this->request('GET', $endpoint, [
            RequestOptions::QUERY => $queryParams,
        ]);
    }

    /**
     * Send a POST request to Allegro API
     *
     * @param string $endpoint API endpoint
     * @param array $data Request body data
     * @return ResponseInterface
     * @throws GuzzleException
     */
    public function post(string $endpoint, array $data = []): ResponseInterface
    {
        return $this->request('POST', $endpoint, [
            RequestOptions::JSON => $data,
        ]);
    }

    /**
     * Send a PUT request to Allegro API
     *
     * @param string $endpoint API endpoint
     * @param array $data Request body data
     * @return ResponseInterface
     * @throws GuzzleException
     */
    public function put(string $endpoint, array $data = []): ResponseInterface
    {
        return $this->request('PUT', $endpoint, [
            RequestOptions::JSON => $data,
        ]);
    }

    /**
     * Send a DELETE request to Allegro API
     *
     * @param string $endpoint API endpoint
     * @return ResponseInterface
     * @throws GuzzleException
     */
    public function delete(string $endpoint): ResponseInterface
    {
        return $this->request('DELETE', $endpoint, []);
    }

    /**
     * Send a PATCH request to Allegro API
     *
     * @param string $endpoint API endpoint
     * @param array $data Request body data
     * @return ResponseInterface
     * @throws GuzzleException
     */
    public function patch(string $endpoint, array $data = []): ResponseInterface
    {
        return $this->request('PATCH', $endpoint, [
            RequestOptions::JSON => $data,
        ]);
    }

    /**
     * Send a request to Allegro API
     *
     * @param string $method HTTP method
     * @param string $endpoint API endpoint
     * @param array $options Request options
     * @return ResponseInterface
     * @throws GuzzleException
     */
    private function request(
        string $method,
        string $endpoint,
        array $options = []
    ): ResponseInterface {
        $endpoint = ltrim($endpoint, '/');

        if (!isset($options[RequestOptions::HEADERS])) {
            $options[RequestOptions::HEADERS] = [];
        }

        // Add authorization header
        $options[RequestOptions::HEADERS]['Authorization'] = "{$this->authData->tokenType} {$this->authData->accessToken}";

        // Add Accept header for JSON response
        $options[RequestOptions::HEADERS]['Accept'] = 'application/vnd.allegro.public.v1+json';

        $this->logger->info("Sending {$method} request to Allegro API", [
            'endpoint' => $endpoint,
            'method' => $method,
        ]);

        try {
            $response = $this->httpClient->request($method, $endpoint, $options);

            $this->logger->info("Received response from Allegro API", [
                'status_code' => $response->getStatusCode(),
                'endpoint' => $endpoint,
                'method' => $method,
            ]);

            return $response;
        } catch (GuzzleException $e) {
            $this->logger->error("Error during Allegro API request", [
                'message' => $e->getMessage(),
                'endpoint' => $endpoint,
                'method' => $method,
                'code' => $e->getCode(),
            ]);

            throw new AllegroException(
                message: $e->getMessage(),
                code: $e->getCode(),
                previous: $e
            );
        }
    }
}
