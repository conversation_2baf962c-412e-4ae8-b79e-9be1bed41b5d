<?php

declare(strict_types=1);

namespace Shared\Integration\Allegro\Adapter;

use GuzzleHttp\ClientInterface;
use Psr\Log\LoggerInterface;
use Shared\Integration\Allegro\Port\AllegroAuthorizationData;

class AllegroClientFactory
{
    public function __construct(
        private readonly LoggerInterface $logger,
        private readonly ?ClientInterface $httpClient = null
    ) {}

    /**
     * Create a new Allegro API client instance with the provided authorization data
     */
    public function create(AllegroAuthorizationData $authData): AllegroClient
    {
        return new AllegroClient(
            $authData,
            $this->logger,
            config('services.allegro.api_url'),
            $this->httpClient
        );
    }
}
