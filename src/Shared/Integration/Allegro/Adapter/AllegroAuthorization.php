<?php

declare(strict_types=1);

namespace Shared\Integration\Allegro\Adapter;

use ErrorException;
use GuzzleHttp\Client;
use GuzzleHttp\ClientInterface;
use GuzzleHttp\Exception\GuzzleException;
use Psr\Log\LoggerInterface;
use Shared\Integration\Abstract\Port\Exception\AllegroAuthorizationException;
use Shared\Integration\Abstract\Port\Authorization\AuthorizationAbstract;
use Shared\Integration\Abstract\Port\Authorization\Method\OAuthCodeFlowAbstractAuthorizationInterface;
use Shared\Integration\Allegro\Port\AllegroAuthorizationData;

class AllegroAuthorization extends AuthorizationAbstract implements OAuthCodeFlowAbstractAuthorizationInterface
{
    private const string AUTHORIZE_DATA_REDIRECT_URI = 'redirect_url';

    private readonly ClientInterface $authClient;

    public function __construct(
        private readonly string $oauthToken,
        private readonly string $oauthAuthorize,
        private readonly string $clientId,
        private readonly string $clientSecret,
        private readonly LoggerInterface $logger,
        ?ClientInterface $authClient = null,
    ) {
        $this->authClient = $authClient ?: new Client([
            'base_uri' => $this->oauthAuthorize,
        ]);
    }

    public function exchangeCode(
        string $code,
        string $redirectUrl
    ): AllegroAuthorizationData {

        $authorizationCode = urlencode($code);

        try {
            return $this->call($redirectUrl, [
                'grant_type' => 'authorization_code',
                'redirect_uri' => $redirectUrl,
                'code' => $authorizationCode,
            ]);
        } catch (GuzzleException $e) {
            throw new AllegroAuthorizationException(
                message: $e->getMessage(),
                code: $e->getCode(),
                previous: $e
            );
        }
    }

    public function authorizationUrl(
        string $redirectUrl
    ): string {
        $params = [
            'response_type' => 'code',
            'client_id' => $this->clientId,
            'redirect_uri' => $redirectUrl,
        ];

        $this->logger->info('Generating Allegro authorize url');

        return sprintf('%s?%s', $this->oauthAuthorize, http_build_query($params));
    }

    public function refreshToken(array $authorizeData): AllegroAuthorizationData
    {
        $oldData = AllegroAuthorizationData::fromArray($authorizeData);

        $this->logger->info('Generating Allegro authorize url');

        try {
            return $this->call($oldData->redirectUrl, [
                'grant_type' => 'refresh_token',
                'redirect_uri' => $oldData->redirectUrl,
                'refresh_token' => $oldData->refreshToken,
            ]);
        } catch (GuzzleException $e) {
            throw new AllegroAuthorizationException(
                message: $e->getMessage(),
                code: $e->getCode(),
                previous: $e
            );
        }
    }

    private function call(
        string $redirectUrl,
        array $formParams
    ): AllegroAuthorizationData {
        $authorization = base64_encode($this->clientId . ':' . $this->clientSecret);

        $params = [
            'form_params' => $formParams,
            'headers' => [
                "Authorization" => "Basic {$authorization}",
                "Content-Type" => "application/x-www-form-urlencoded"
            ],
        ];

        $response = $this->authClient->request('POST', $this->oauthToken, $params);
        $data = json_decode($response->getBody()->getContents(), true);

        // required during refreshing the token
        $data[self::AUTHORIZE_DATA_REDIRECT_URI] = $redirectUrl;

        try {
            return AllegroAuthorizationData::fromArray($data);
        } catch (ErrorException $e) {
            throw new AllegroAuthorizationException(
                message: $e->getMessage(),
                code: $e->getCode(),
                previous: $e
            );
        }
    }
}
