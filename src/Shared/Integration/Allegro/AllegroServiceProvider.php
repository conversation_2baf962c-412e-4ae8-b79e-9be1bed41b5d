<?php

declare(strict_types=1);

namespace Shared\Integration\Allegro;

use Illuminate\Support\ServiceProvider;
use Psr\Log\LoggerInterface;
use Shared\Integration\Allegro\Adapter\AllegroAuthorization;
use Shared\Integration\Allegro\Port\AllegroIntegration;

class AllegroServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->bind(
            AllegroIntegration::class,
            fn() => new AllegroIntegration(
                new AllegroAuthorization(
                    config('services.allegro.oauth_token'),
                    config('services.allegro.oauth_authorize'),
                    config('services.allegro.client_id'),
                    config('services.allegro.client_secret'),
                    app(LoggerInterface::class)
                )
            )
        );
    }
}
