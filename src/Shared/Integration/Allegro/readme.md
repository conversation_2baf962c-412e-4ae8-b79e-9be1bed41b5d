## Links

* [Allegro Developers](https://developer.allegro.pl/)

Produkcja:

* Adres wywołania metod - https://api.allegro.pl/
* Rejestracja aplikacji - https://apps.developer.allegro.pl/
* Adres do autoryzacji - https://allegro.pl/auth/oauth/

Sandbox:

* [Allegro Sandbox](https://allegro.pl.allegrosandbox.pl/)
* Adres wywołania metod - https://api.allegro.pl.allegrosandbox.pl/
* Rejestracja aplikacji - https://apps.developer.allegro.pl.allegrosandbox.pl/
* Adres do autoryzacji - https://allegro.pl.allegrosandbox.pl/auth/oauth/

## Konfiguracja sandbox

1. Zakładamy nowe konto
2. Aktywujemy email
3. Aktywujemy telefon - sms nie dochodzi - podajemy kod 123456
4. Aktwujemy sprzedaż po linkiem https://allegro.pl.allegrosandbox.pl/lokalnie/c2c/sales-activation
5. Na aktywacje trzeba pocze<PERSON>
6. Zakładamy aplikacje https://apps.developer.allegro.pl.allegrosandbox.pl/
