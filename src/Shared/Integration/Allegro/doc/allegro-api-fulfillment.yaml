openapi: 3.0.0
servers:
  - url: https://api.{environment}
    variables:
      environment:
        default: allegro.pl
        enum:
          - allegro.pl                    # Production server
          - allegro.pl.allegrosandbox.pl  # Sandbox server
info:
  description:
    'https://developer.allegro.pl/about


    Documentation is generated from [this OpenAPI 3.0 specification file](https://developer.allegro.pl/swagger.yaml).

    To start working with our API, you can also check our [official Allegro REST API public collection](https://www.postman.com/allegro-rest-api/allegro-rest-api/collection/4puh6ls/allegro-rest-api) in Postman.'
  version: 'latest'
  title: Allegro REST API - Fulfillment
  termsOfService: 'https://developer.allegro.pl/rules/'
  contact:
    name: "API Support"
    url: 'https://github.com/allegro/allegro-api/issues'

tags:
  - name: Fulfillment

x-tagGroups:
  - name: Fulfillment
    tags:
      - Fulfillment

security:
  - bearer-token-for-user: []

paths:
  /fulfillment/advance-ship-notices:
    get:
      summary: 'Get list of Advance Ship Notices'
      description: 'Use this resource to get a list of Advance Ship Notices. Read more: <a href="../../tutorials/one-fulfillment-by-allegro-0ADwgOLqWSw#jak-przegladac-utworzone-awizo" target="_blank">PL</a> / <a href="../../tutorials/one-fulfillment-by-allegro-4R9dXyMPlc9#how-to-get-created-advance-ship-notices" target="_blank">EN</a>.'
      tags:
        - Fulfillment
      parameters:
        - name: offset
          in: query
          description: Index of first returned Advance Ship Notice from all results.
          required: false
          schema:
            type: integer
            default: 0
        - name: limit
          in: query
          description: Maximum number of Advance Ship Notices in response.
          required: false
          schema:
            type: integer
            default: 100
      responses:
        '200':
          description: OK
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/AdvanceShipNoticeList'
      security:
        - bearer-token-for-user:
            - allegro:api:fulfillment:read
    post:
      summary: 'Create an Advance Ship Notice'
      description: 'Use this resource to create an Advance Ship Notice. Read more: <a href="../../tutorials/one-fulfillment-by-allegro-0ADwgOLqWSw#utworz-draft-awizo" target="_blank">PL</a> / <a href="../../tutorials/one-fulfillment-by-allegro-4R9dXyMPlc9#create-a-draft-of-the-advance-ship-notice" target="_blank">EN</a>.'
      requestBody:
        content:
          application/vnd.allegro.public.v1+json:
            schema:
              $ref: '#/components/schemas/AdvanceShipNoticeRequest'
      tags:
        - Fulfillment
      responses:
        '201':
          description: Created
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/AdvanceShipNotice'
        '400':
          description: Bad request
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/ErrorsHolder'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/ErrorsHolder'
        '409':
          description: Conflict
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/ErrorsHolder'
        '422':
          description: Unprocessable Entity
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/ErrorsHolder'
      security:
        - bearer-token-for-user:
            - allegro:api:fulfillment:write
            
  /fulfillment/advance-ship-notices/{id}:
    get:
      summary: 'Get single Advance Ship Notice'
      description: 'Use this resource to get an Advance Ship Notice. Read more: <a href="../../tutorials/one-fulfillment-by-allegro-0ADwgOLqWSw#jak-przegladac-utworzone-awizo" target="_blank">PL</a> / <a href="../../tutorials/one-fulfillment-by-allegro-4R9dXyMPlc9#how-to-get-created-advance-ship-notices" target="_blank">EN</a>.'
      tags:
        - Fulfillment
      parameters:
        - name: id
          in: path
          description: Advance Ship Notice identifier.
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/AdvanceShipNotice'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/ErrorsHolder'
        '404':
          description: Not Found
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/ErrorsHolder'
      security:
        - bearer-token-for-user:
            - allegro:api:fulfillment:read
    put:
      summary: 'Update Advance Ship Notice'
      description: >-
        Use this resource to update an Advance Ship Notice. Any content property update will clear labels property.
        Use Create labels command to create new labels for provided content.
      tags:
        - Fulfillment
      parameters:
        - name: id
          in: path
          description: Advance Ship Notice identifier.
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/vnd.allegro.public.v1+json:
            schema:
              $ref: '#/components/schemas/AdvanceShipNoticeRequest'
      responses:
        '200':
          description: OK
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/AdvanceShipNotice'
        '400':
          description: Bad request
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/ErrorsHolder'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/ErrorsHolder'
        '404':
          description: Not Found
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/ErrorsHolder'
        '409':
          description: Conflict
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/ErrorsHolder'
        '422':
          description: Unprocessable Entity
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/ErrorsHolder'
      security:
        - bearer-token-for-user:
            - allegro:api:fulfillment:write

components:
  securitySchemes:
    bearer-token-for-user:
      type: oauth2
      description: >-
        **Important!** Do not require the user of your application to register a new instance of the application and send you Client_ID and Client_Secret. Regardless of the authorization method, the application must run on a single key (Client_ID). For more information <a href="../../tutorials/uwierzytelnianie-i-autoryzacja-zlq9e75GdIR#authorization-code-flow" target="_blank">PL</a> / <a href="../../tutorials/authentication-and-authorization-m09BlVyo7iY#authorization-code-flow" target="_blank">EN</a> to read about authorization code flow
        or <a href="../../tutorials/uwierzytelnianie-i-autoryzacja-zlq9e75GdIR#device-flow" target="_blank">PL</a> / <a href="../../tutorials/authentication-and-authorization-m09BlVyo7iY#device-flow" target="_blank">EN</a> to read about the device code flow.
      flows:
        authorizationCode:
          authorizationUrl: https://allegro.pl/auth/oauth/authorize
          tokenUrl: https://allegro.pl/auth/oauth/token
          scopes: {}
        x-deviceCode:
          authorizationUrl: https://allegro.pl/skojarz-aplikacje
          tokenUrl: https://allegro.pl/auth/oauth/token
          scopes: {}
          
  schemas:
    AdvanceShipNoticeList:
      type: object
      properties:
        advanceShipNotices:
          type: array
          items:
            $ref: '#/components/schemas/AdvanceShipNotice'
        count:
          type: integer
          description: Number of returned objects.
        totalCount:
          type: integer
          description: Total number of objects.
          
    AdvanceShipNotice:
      type: object
      properties:
        id:
          type: string
          description: Advance Ship Notice identifier.
        status:
          type: string
          description: Advance Ship Notice status.
          enum:
            - DRAFT
            - READY_FOR_PROCESSING
            - PROCESSING
            - PROCESSED
            - CANCELLED
        createdAt:
          type: string
          format: date-time
          description: Advance Ship Notice creation date.
        updatedAt:
          type: string
          format: date-time
          description: Advance Ship Notice last modification date.
        content:
          $ref: '#/components/schemas/AdvanceShipNoticeContent'
        labels:
          $ref: '#/components/schemas/AdvanceShipNoticeLabels'
          
    AdvanceShipNoticeRequest:
      type: object
      properties:
        content:
          $ref: '#/components/schemas/AdvanceShipNoticeContent'
          
    AdvanceShipNoticeContent:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/AdvanceShipNoticeItem'
        shipping:
          $ref: '#/components/schemas/AdvanceShipNoticeShipping'
          
    AdvanceShipNoticeItem:
      type: object
      properties:
        productId:
          type: string
          description: Product identifier.
        quantity:
          type: integer
          description: Item quantity.
          
    AdvanceShipNoticeShipping:
      type: object
      properties:
        carrier:
          type: string
          description: Shipping carrier.
        trackingNumber:
          type: string
          description: Shipping tracking number.
          
    AdvanceShipNoticeLabels:
      type: object
      properties:
        file:
          type: string
          description: Labels file URL.
          
    ErrorsHolder:
      type: object
      properties:
        errors:
          type: array
          description: List of errors.
          items:
            $ref: '#/components/schemas/Error'
            
    Error:
      type: object
      properties:
        code:
          type: string
          description: Error code.
        message:
          type: string
          description: Error message.
        path:
          type: string
          description: Error path.
