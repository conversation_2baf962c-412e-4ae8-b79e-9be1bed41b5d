openapi: 3.0.0
servers:
  - url: https://api.{environment}
    variables:
      environment:
        default: allegro.pl
        enum:
          - allegro.pl                    # Production server
          - allegro.pl.allegrosandbox.pl  # Sandbox server
info:
  description:
    'https://developer.allegro.pl/about


    Documentation is generated from [this OpenAPI 3.0 specification file](https://developer.allegro.pl/swagger.yaml).

    To start working with our API, you can also check our [official Allegro REST API public collection](https://www.postman.com/allegro-rest-api/allegro-rest-api/collection/4puh6ls/allegro-rest-api) in Postman.'
  version: 'latest'
  title: Allegro REST API - Billing
  termsOfService: 'https://developer.allegro.pl/rules/'
  contact:
    name: "API Support"
    url: 'https://github.com/allegro/allegro-api/issues'

tags:
  - name: Billing
  - name: Offer variants

x-tagGroups:
  - name: Billing
    tags:
      - Billing
      - Offer variants

security:
  - bearer-token-for-user: []

paths:
  /billing/billing-types:
    get:
      tags:
        - Billing
      summary: 'Get a list of billing types'
      description: 'Use this resource to get a list of billing types.'
      operationId: getBillingTypes
      responses:
        '200':
          description: OK
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/BillingTypes'
        '401':
          description: Unauthorized
      security:
      - bearer-token-for-user:
        - allegro:api:billing:read
        
  /sale/offer-variants:
    post:
      tags:
        - Offer variants
      summary: 'Create variant set'
      description: 'Use this resource to create variant set for offer variants. Read more: <a href="../../tutorials/jak-jednym-requestem-wystawic-oferte-z-wariantami-D7Kj9gw4xFA#jak-utworzyc-oferte-z-wariantami" target="_blank">PL</a> / <a href="../../tutorials/list-offer-with-variants-in-one-request-D7Kj9M71Bu6#how-to-create-an-offer-with-variants" target="_blank">EN</a>.'
      operationId: createVariantSet
      requestBody:
        content:
          application/vnd.allegro.public.v1+json:
            schema:
              $ref: '#/components/schemas/VariantSetDraft'
        required: true
      responses:
        '201':
          description: Created
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/VariantSetResponse'
        '400':
          description: Bad request
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/ErrorsHolder'
        '401':
          description: Unauthorized
        '422':
          description: Unprocessable Entity
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/ErrorsHolder'
      security:
      - bearer-token-for-user:
        - allegro:api:billing:read

components:
  securitySchemes:
    bearer-token-for-user:
      type: oauth2
      description: >-
        **Important!** Do not require the user of your application to register a new instance of the application and send you Client_ID and Client_Secret. Regardless of the authorization method, the application must run on a single key (Client_ID). For more information <a href="../../tutorials/uwierzytelnianie-i-autoryzacja-zlq9e75GdIR#authorization-code-flow" target="_blank">PL</a> / <a href="../../tutorials/authentication-and-authorization-m09BlVyo7iY#authorization-code-flow" target="_blank">EN</a> to read about authorization code flow
        or <a href="../../tutorials/uwierzytelnianie-i-autoryzacja-zlq9e75GdIR#device-flow" target="_blank">PL</a> / <a href="../../tutorials/authentication-and-authorization-m09BlVyo7iY#device-flow" target="_blank">EN</a> to read about the device code flow.
      flows:
        authorizationCode:
          authorizationUrl: https://allegro.pl/auth/oauth/authorize
          tokenUrl: https://allegro.pl/auth/oauth/token
          scopes: {}
        x-deviceCode:
          authorizationUrl: https://allegro.pl/skojarz-aplikacje
          tokenUrl: https://allegro.pl/auth/oauth/token
          scopes: {}
          
  schemas:
    BillingTypes:
      type: object
      properties:
        billingTypes:
          type: array
          items:
            $ref: '#/components/schemas/BillingType'
            
    BillingType:
      type: object
      properties:
        id:
          type: string
          description: Billing type ID.
        description:
          type: string
          description: Billing type description.
          
    VariantSetDraft:
      type: object
      properties:
        name:
          type: string
          description: Variant set name.
        parameters:
          type: array
          description: List of variant parameters.
          items:
            $ref: '#/components/schemas/VariantParameter'
        offers:
          type: array
          description: List of variant offers.
          items:
            $ref: '#/components/schemas/VariantOffer'
            
    VariantParameter:
      type: object
      properties:
        id:
          type: string
          description: Parameter ID.
        name:
          type: string
          description: Parameter name.
        values:
          type: array
          description: List of parameter values.
          items:
            type: string
            
    VariantOffer:
      type: object
      properties:
        id:
          type: string
          description: Offer ID.
        parameters:
          type: array
          description: List of offer parameters.
          items:
            $ref: '#/components/schemas/VariantOfferParameter'
            
    VariantOfferParameter:
      type: object
      properties:
        id:
          type: string
          description: Parameter ID.
        value:
          type: string
          description: Parameter value.
          
    VariantSetResponse:
      type: object
      properties:
        id:
          type: string
          description: Variant set ID.
        name:
          type: string
          description: Variant set name.
        parameters:
          type: array
          description: List of variant parameters.
          items:
            $ref: '#/components/schemas/VariantParameter'
        offers:
          type: array
          description: List of variant offers.
          items:
            $ref: '#/components/schemas/VariantOffer'
            
    ErrorsHolder:
      type: object
      properties:
        errors:
          type: array
          description: List of errors.
          items:
            $ref: '#/components/schemas/Error'
            
    Error:
      type: object
      properties:
        code:
          type: string
          description: Error code.
        message:
          type: string
          description: Error message.
        path:
          type: string
          description: Error path.
