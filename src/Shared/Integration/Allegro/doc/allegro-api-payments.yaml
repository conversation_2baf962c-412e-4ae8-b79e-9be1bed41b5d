openapi: 3.0.0
servers:
  - url: https://api.{environment}
    variables:
      environment:
        default: allegro.pl
        enum:
          - allegro.pl                    # Production server
          - allegro.pl.allegrosandbox.pl  # Sandbox server
info:
  description:
    'https://developer.allegro.pl/about


    Documentation is generated from [this OpenAPI 3.0 specification file](https://developer.allegro.pl/swagger.yaml).

    To start working with our API, you can also check our [official Allegro REST API public collection](https://www.postman.com/allegro-rest-api/allegro-rest-api/collection/4puh6ls/allegro-rest-api) in Postman.'
  version: 'latest'
  title: Allegro REST API - Payments
  termsOfService: 'https://developer.allegro.pl/rules/'
  contact:
    name: "API Support"
    url: 'https://github.com/allegro/allegro-api/issues'

tags:
  - name: Payments
  - name: Billing
  - name: Auctions and Bidding

x-tagGroups:
  - name: Payments
    tags:
      - Payments
      - Billing
      - Auctions and Bidding

security:
  - bearer-token-for-user: []

paths:
  /billing/billing-entries:
    get:
      tags:
        - Billing
      summary: 'Get a list of billing entries'
      description: 'Use this resource to get a list of billing entries. Read more: <a href="../../tutorials/jak-sprawdzic-oplaty-nn9DOL5PASX#historia-operacji-billingowych" target="_blank">PL</a> / <a href="../../tutorials/how-to-check-the-fees-3A4BWOYlvTp#billing-operations-history" target="_blank">EN</a>.'
      operationId: getBillingEntries
      parameters:
        - name: occurred.gte
          in: query
          description: Minimum date and time in ISO 8601 format.
          required: false
          schema:
            type: string
            format: date-time
        - name: occurred.lte
          in: query
          description: Maximum date and time in ISO 8601 format.
          required: false
          schema:
            type: string
            format: date-time
        - name: type.id
          in: query
          description: Billing type ID.
          required: false
          schema:
            type: string
        - name: offer.id
          in: query
          description: Offer ID.
          required: false
          schema:
            type: string
        - name: offset
          in: query
          description: Index of first returned billing entry from all search results.
          required: false
          schema:
            type: integer
            default: 0
        - name: limit
          in: query
          description: Maximum number of billing entries in response.
          required: false
          schema:
            type: integer
            default: 100
      responses:
        '200':
          description: OK
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/BillingEntries'
        '401':
          description: Unauthorized
        '404':
          description: Not Found
      security:
      - bearer-token-for-user:
        - allegro:api:payments:read
        
  /payments/payment-operations:
    get:
      tags:
        - Payments
      summary: 'Get a list of refunded payments'
      description: 'Get a list of refunded payments. Read more: <a href="../../tutorials/jak-obslugiwac-zamowienia-GRaj0qyvwtR#jak-pobrac-liste-zwrotow-platnosci" target="_blank">PL</a> / <a href="../../tutorials/process-orders-PgPMlWDr8Cv#how-to-retrieve-a-list-of-refunded-payment" target="_blank">EN</a>.'
      operationId: getPaymentOperations
      parameters:
        - name: wallet.id
          in: query
          description: Wallet ID.
          required: false
          schema:
            type: string
        - name: payment.id
          in: query
          description: Payment ID.
          required: false
          schema:
            type: string
        - name: participant.id
          in: query
          description: Participant ID.
          required: false
          schema:
            type: string
        - name: type
          in: query
          description: Payment operation type.
          required: false
          schema:
            type: string
            enum:
              - REFUND
              - CONTRIBUTION
              - PAYOUT
        - name: offset
          in: query
          description: Index of first returned payment operation from all search results.
          required: false
          schema:
            type: integer
            default: 0
        - name: limit
          in: query
          description: Maximum number of payment operations in response.
          required: false
          schema:
            type: integer
            default: 100
      responses:
        '200':
          description: OK
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/PaymentOperations'
        '401':
          description: Unauthorized
        '404':
          description: Not Found
      security:
      - bearer-token-for-user:
        - allegro:api:payments:write
        
  /bidding/offers/{offerId}/bid:
    put:
      tags:
      - Auctions and Bidding
      operationId: placeBid
      parameters:
        - name: offerId
          in: path
          description: Offer ID.
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/vnd.allegro.public.v1+json:
            schema:
              $ref: '#/components/schemas/BidRequest'
        required: true
      responses:
        '200':
          description: OK
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/BidResponse'
        '400':
          description: Bad request
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/ErrorsHolder'
        '401':
          description: Unauthorized
        '404':
          description: Not Found
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/ErrorsHolder'
        '422':
          description: Unprocessable Entity
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/ErrorsHolder'
      security:
      - bearer-token-for-user:
        - allegro:api:payments:read

components:
  securitySchemes:
    bearer-token-for-user:
      type: oauth2
      description: >-
        **Important!** Do not require the user of your application to register a new instance of the application and send you Client_ID and Client_Secret. Regardless of the authorization method, the application must run on a single key (Client_ID). For more information <a href="../../tutorials/uwierzytelnianie-i-autoryzacja-zlq9e75GdIR#authorization-code-flow" target="_blank">PL</a> / <a href="../../tutorials/authentication-and-authorization-m09BlVyo7iY#authorization-code-flow" target="_blank">EN</a> to read about authorization code flow
        or <a href="../../tutorials/uwierzytelnianie-i-autoryzacja-zlq9e75GdIR#device-flow" target="_blank">PL</a> / <a href="../../tutorials/authentication-and-authorization-m09BlVyo7iY#device-flow" target="_blank">EN</a> to read about the device code flow.
      flows:
        authorizationCode:
          authorizationUrl: https://allegro.pl/auth/oauth/authorize
          tokenUrl: https://allegro.pl/auth/oauth/token
          scopes: {}
        x-deviceCode:
          authorizationUrl: https://allegro.pl/skojarz-aplikacje
          tokenUrl: https://allegro.pl/auth/oauth/token
          scopes: {}
          
  schemas:
    BillingEntries:
      type: object
      properties:
        billingEntries:
          type: array
          items:
            $ref: '#/components/schemas/BillingEntry'
        count:
          type: integer
          description: Number of returned objects.
        totalCount:
          type: integer
          description: Total number of objects.
          
    BillingEntry:
      type: object
      properties:
        id:
          type: string
          description: Billing entry ID.
        occurred:
          type: string
          format: date-time
          description: Date and time of the operation.
        type:
          $ref: '#/components/schemas/BillingType'
        value:
          $ref: '#/components/schemas/Price'
        balance:
          $ref: '#/components/schemas/Price'
        order:
          $ref: '#/components/schemas/BillingEntryOrder'
        offer:
          $ref: '#/components/schemas/BillingEntryOffer'
          
    BillingEntryOrder:
      type: object
      properties:
        id:
          type: string
          description: Order ID.
          
    BillingEntryOffer:
      type: object
      properties:
        id:
          type: string
          description: Offer ID.
          
    PaymentOperations:
      type: object
      properties:
        paymentOperations:
          type: array
          items:
            $ref: '#/components/schemas/PaymentOperation'
        count:
          type: integer
          description: Number of returned objects.
        totalCount:
          type: integer
          description: Total number of objects.
          
    PaymentOperation:
      type: object
      properties:
        id:
          type: string
          description: Payment operation ID.
        payment:
          $ref: '#/components/schemas/Payment'
        type:
          type: string
          description: Payment operation type.
          enum:
            - REFUND
            - CONTRIBUTION
            - PAYOUT
        value:
          $ref: '#/components/schemas/Price'
          
    Payment:
      type: object
      properties:
        id:
          type: string
          description: Payment ID.
          
    Price:
      type: object
      properties:
        amount:
          type: string
          description: The amount provided in a string format to avoid rounding errors.
        currency:
          type: string
          description: The currency provided as a 3-letter code in accordance with ISO 4217 standard (https://en.wikipedia.org/wiki/ISO_4217).
          
    BidRequest:
      type: object
      properties:
        maxAmount:
          type: string
          description: Maximum bid amount.
          
    BidResponse:
      type: object
      properties:
        maxBidExceeded:
          type: boolean
          description: Indicates if the maximum bid amount has been exceeded.
        minBidAmount:
          type: string
          description: Minimum bid amount.
          
    ErrorsHolder:
      type: object
      properties:
        errors:
          type: array
          description: List of errors.
          items:
            $ref: '#/components/schemas/Error'
            
    Error:
      type: object
      properties:
        code:
          type: string
          description: Error code.
        message:
          type: string
          description: Error message.
        path:
          type: string
          description: Error path.
