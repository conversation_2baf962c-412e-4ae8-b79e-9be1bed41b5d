openapi: 3.0.0
servers:
  - url: https://api.{environment}
    variables:
      environment:
        default: allegro.pl
        enum:
          - allegro.pl                    # Production server
          - allegro.pl.allegrosandbox.pl  # Sandbox server
info:
  description:
    'https://developer.allegro.pl/about


    Documentation is generated from [this OpenAPI 3.0 specification file](https://developer.allegro.pl/swagger.yaml).

    To start working with our API, you can also check our [official Allegro REST API public collection](https://www.postman.com/allegro-rest-api/allegro-rest-api/collection/4puh6ls/allegro-rest-api) in Postman.'
  version: 'latest'
  title: Allegro REST API - Shipments
  termsOfService: 'https://developer.allegro.pl/rules/'
  contact:
    name: "API Support"
    url: 'https://github.com/allegro/allegro-api/issues'

tags:
  - name: Shipment management

x-tagGroups:
  - name: Shipments
    tags:
      - Shipment management

security:
  - bearer-token-for-user: []

paths:
  /shipment-management/shipments/create-commands:
    post:
      tags:
        - Shipment management
      summary: Create new shipment
      description: 'Use this resource to create a new shipment. Read more: <a href="../../tutorials/jak-zarzad<PERSON>-przesylkami-przez-api-paczkomaty-inpost-dhl-ups-fedex-dpd-pocztex-gls-YVn8eldRdFR#jak-utworzyc-nowa-przesylke" target="_blank">PL</a> / <a href="../../tutorials/how-to-manage-shipments-via-api-paczkomaty-inpost-dhl-ups-fedex-dpd-pocztex-gls-LgyVn3OKxC0#how-to-create-a-new-shipment" target="_blank">EN</a>.'
      operationId: createShipment
      requestBody:
        content:
          application/vnd.allegro.public.v1+json:
            schema:
              $ref: '#/components/schemas/ShipmentCreateCommand'
        required: true
      responses:
        '201':
          description: Created
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/ShipmentCreateCommandOutput'
        '400':
          description: Bad request
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/ErrorsHolder'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/ErrorsHolder'
        '422':
          description: Unprocessable Entity
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/ErrorsHolder'
      security:
        - bearer-token-for-user:
            - allegro:api:shipments:write
            
  /shipment-management/shipments/create-commands/{commandId}:
    get:
      tags:
        - Shipment management
      summary: Get shipment creation command status
      description: 'Use this resource to get shipment creation command status. Read more: <a href="../../tutorials/jak-zarzadzac-przesylkami-przez-api-paczkomaty-inpost-dhl-ups-fedex-dpd-pocztex-gls-YVn8eldRdFR#jak-sprawdzic-status-utworzenia-przesylki" target="_blank">PL</a> / <a href="../../tutorials/how-to-manage-shipments-via-api-paczkomaty-inpost-dhl-ups-fedex-dpd-pocztex-gls-LgyVn3OKxC0#how-to-check-shipment-creation-status" target="_blank">EN</a>.'
      operationId: getShipmentCreationStatus
      parameters:
        - name: commandId
          in: path
          description: Command identifier.
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/ShipmentCreateCommandOutput'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/ErrorsHolder'
        '404':
          description: Not Found
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/ErrorsHolder'
      security:
        - bearer-token-for-user:
            - allegro:api:shipments:write
            
  /shipment-management/shipments/cancel-commands:
    post:
      tags:
        - Shipment management
      summary: Cancel shipment
      description: 'Use this resource to cancel shipment. Read more: <a href="../../tutorials/jak-zarzadzac-przesylkami-przez-api-paczkomaty-inpost-dhl-ups-fedex-dpd-pocztex-gls-YVn8eldRdFR#jak-anulowac-przesylke" target="_blank">PL</a> / <a href="../../tutorials/how-to-manage-shipments-via-api-paczkomaty-inpost-dhl-ups-fedex-dpd-pocztex-gls-LgyVn3OKxC0#how-to-cancel-a-shipment" target="_blank">EN</a>.'
      operationId: cancelShipment
      requestBody:
        content:
          application/vnd.allegro.public.v1+json:
            schema:
              $ref: '#/components/schemas/ShipmentCancelCommand'
        required: true
      responses:
        '201':
          description: Created
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/ShipmentCancelCommandOutput'
        '400':
          description: Bad request
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/ErrorsHolder'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/ErrorsHolder'
        '422':
          description: Unprocessable Entity
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/ErrorsHolder'
      security:
        - bearer-token-for-user:
            - allegro:api:shipments:write

components:
  securitySchemes:
    bearer-token-for-user:
      type: oauth2
      description: >-
        **Important!** Do not require the user of your application to register a new instance of the application and send you Client_ID and Client_Secret. Regardless of the authorization method, the application must run on a single key (Client_ID). For more information <a href="../../tutorials/uwierzytelnianie-i-autoryzacja-zlq9e75GdIR#authorization-code-flow" target="_blank">PL</a> / <a href="../../tutorials/authentication-and-authorization-m09BlVyo7iY#authorization-code-flow" target="_blank">EN</a> to read about authorization code flow
        or <a href="../../tutorials/uwierzytelnianie-i-autoryzacja-zlq9e75GdIR#device-flow" target="_blank">PL</a> / <a href="../../tutorials/authentication-and-authorization-m09BlVyo7iY#device-flow" target="_blank">EN</a> to read about the device code flow.
      flows:
        authorizationCode:
          authorizationUrl: https://allegro.pl/auth/oauth/authorize
          tokenUrl: https://allegro.pl/auth/oauth/token
          scopes: {}
        x-deviceCode:
          authorizationUrl: https://allegro.pl/skojarz-aplikacje
          tokenUrl: https://allegro.pl/auth/oauth/token
          scopes: {}
          
  schemas:
    ShipmentCreateCommand:
      type: object
      properties:
        orderNumber:
          type: string
          description: Order number.
        sender:
          $ref: '#/components/schemas/Address'
        receiver:
          $ref: '#/components/schemas/Address'
        service:
          $ref: '#/components/schemas/ShipmentService'
        parcels:
          type: array
          description: List of parcels.
          items:
            $ref: '#/components/schemas/Parcel'
            
    Address:
      type: object
      properties:
        firstName:
          type: string
          description: First name.
        lastName:
          type: string
          description: Last name.
        street:
          type: string
          description: Street.
        postalCode:
          type: string
          description: Postal code.
        city:
          type: string
          description: City.
        countryCode:
          type: string
          description: Country code.
        company:
          type: string
          description: Company name.
        phoneNumber:
          type: string
          description: Phone number.
        email:
          type: string
          description: Email.
          
    ShipmentService:
      type: object
      properties:
        id:
          type: string
          description: Service ID.
        name:
          type: string
          description: Service name.
        carrier:
          type: string
          description: Carrier name.
          
    Parcel:
      type: object
      properties:
        id:
          type: string
          description: Parcel ID.
        dimensions:
          $ref: '#/components/schemas/ParcelDimensions'
        weight:
          $ref: '#/components/schemas/ParcelWeight'
        description:
          type: string
          description: Parcel description.
          
    ParcelDimensions:
      type: object
      properties:
        length:
          type: number
          description: Length in cm.
        width:
          type: number
          description: Width in cm.
        height:
          type: number
          description: Height in cm.
          
    ParcelWeight:
      type: object
      properties:
        value:
          type: number
          description: Weight value.
        unit:
          type: string
          description: Weight unit.
          enum:
            - KILOGRAM
            - GRAM
            
    ShipmentCreateCommandOutput:
      type: object
      properties:
        id:
          type: string
          description: Command ID.
        status:
          type: string
          description: Command status.
          enum:
            - PENDING
            - PROCESSING
            - SUCCEEDED
            - FAILED
        errors:
          type: array
          description: List of errors.
          items:
            $ref: '#/components/schemas/Error'
        shipmentId:
          type: string
          description: Shipment ID.
        trackingNumber:
          type: string
          description: Tracking number.
        labelUrl:
          type: string
          description: Label URL.
          
    ShipmentCancelCommand:
      type: object
      properties:
        id:
          type: string
          description: Shipment ID.
          
    ShipmentCancelCommandOutput:
      type: object
      properties:
        id:
          type: string
          description: Command ID.
        status:
          type: string
          description: Command status.
          enum:
            - PENDING
            - PROCESSING
            - SUCCEEDED
            - FAILED
        errors:
          type: array
          description: List of errors.
          items:
            $ref: '#/components/schemas/Error'
            
    ErrorsHolder:
      type: object
      properties:
        errors:
          type: array
          description: List of errors.
          items:
            $ref: '#/components/schemas/Error'
            
    Error:
      type: object
      properties:
        code:
          type: string
          description: Error code.
        message:
          type: string
          description: Error message.
        path:
          type: string
          description: Error path.
