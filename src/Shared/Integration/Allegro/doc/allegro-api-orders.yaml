openapi: 3.0.0
servers:
  - url: https://api.{environment}
    variables:
      environment:
        default: allegro.pl
        enum:
          - allegro.pl                    # Production server
          - allegro.pl.allegrosandbox.pl  # Sandbox server
info:
  description:
    'https://developer.allegro.pl/about


    Documentation is generated from [this OpenAPI 3.0 specification file](https://developer.allegro.pl/swagger.yaml).

    To start working with our API, you can also check our [official Allegro REST API public collection](https://www.postman.com/allegro-rest-api/allegro-rest-api/collection/4puh6ls/allegro-rest-api) in Postman.'
  version: 'latest'
  title: Allegro REST API - Orders
  termsOfService: 'https://developer.allegro.pl/rules/'
  contact:
    name: "API Support"
    url: 'https://github.com/allegro/allegro-api/issues'

tags:
  - name: Order management
  - name: Customer returns
  - name: Commission refunds

x-tagGroups:
  - name: Orders
    tags:
      - Order management
      - Customer returns
      - Commission refunds

security:
  - bearer-token-for-user: []

paths:
  /order/event-stats:
    get:
      tags:
        - Order management
      summary: 'Get order events statistics'
      description: >-
        Use this resource to returns object that contains event id and occurrence date of the latest event.
        It gives you current starting point for reading events. Read more: <a href="../../tutorials/jak-obslugiwac-zamowienia-GRaj0qyvwtR#jak-znalezc-najnowsze-zdarzenie" target="_blank">PL</a> / <a href="../../tutorials/process-orders-PgPMlWDr8Cv#how-to-find-the-newest-event" target="_blank">EN</a>.
      operationId: getOrderEventsStatisticsUsingGET
      responses:
        '200':
          description: OK
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/OrderEventStats'
        '401':
          description: Unauthorized
      security:
      - bearer-token-for-user:
        - allegro:api:orders:read

  /order/checkout-forms:
    get:
      tags:
        - Order management
      summary: "Get the user's orders"
      description: >-
        Use this resource to get an order list. Read more: <a href="../../tutorials/jak-obslugiwac-zamowienia-GRaj0qyvwtR#lista-zamowien" target="_blank">PL</a> / <a href="../../tutorials/process-orders-PgPMlWDr8Cv#order-list" target="_blank">EN</a>.
      operationId: getListOfOrdersUsingGET
      parameters:
        - name: offset
          in: query
          description: Index of first returned checkout-form from all search results.
          required: false
          schema:
            type: integer
            minimum: 0
            default: 0
        - name: limit
          in: query
          description: >-
            Maximum number of checkout-forms in response.
          required: false
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 100
        - name: status
          in: query
          schema:
            type: string
          description: >-
            Specify status value that checkout-forms must have to be included in the output.
            Allowed values are:
              * `BOUGHT`: purchase without checkout form filled in.
              * `FILLED_IN`: checkout form filled in but payment is not completed yet so data could still change.
              * `READY_FOR_PROCESSING`: payment completed. Purchase is ready for processing.
              * `CANCELLED`: purchase cancelled by buyer.
        - name: fulfillment.status
          in: query
          schema:
            type: string
          description: >-
            Specify seller status value that checkout-forms must have to be included in the output.
            Allowed values are:
              * `NEW`
              * `PROCESSING`
              * `READY_FOR_SHIPMENT`
              * `READY_FOR_PICKUP`
              * `SENT`
              * `PICKED_UP`
              * `CANCELLED`
              * `SUSPENDED`
              * `RETURNED`.
        - name: fulfillment.shipmentSummary.lineItemsSent
          in: query
          schema:
            type: string
          description: >-
            Specify filter for line items sending status.
            Allowed values are:
              * `NONE`: none of line items have tracking number specified
              * `SOME`: some of line items have tracking number specified
              * `ALL`: all of line items have tracking number specified.
        - name: lineItems.boughtAt.lte
          in: query
          schema:
            type: string
            format: date-time
          description: Latest line item bought date. The upper bound of date time range from which checkout forms will be taken.
        - name: lineItems.boughtAt.gte
          in: query
          schema:
            type: string
            format: date-time
          description: Latest line item bought date. The lower bound of date time range from which checkout forms will be taken.
        - name: payment.id
          in: query
          schema:
            type: string
          description: Find checkout-forms having specified payment id.
        - name: surcharges.id
          in: query
          schema:
            type: string
          description: Find checkout-forms having specified surcharge id.
        - name: delivery.method.id
          in: query
          schema:
            type: string
          description: Find checkout-forms having specified delivery method id.
        - name: buyer.login
          in: query
          schema:
            type: string
          description: Find checkout-forms having specified buyer login.
        - name: marketplace.id
          in: query
          schema:
            type: string
          description: Find checkout-forms of orders purchased on specified marketplace.
        - name: updatedAt.lte
          in: query
          schema:
            type: string
            format: date-time
          description: Checkout form last modification date. The upper bound of date time range from which checkout forms will be taken.
        - name: updatedAt.gte
          in: query
          schema:
            type: string
            format: date-time
          description: Checkout form last modification date. The lower bound of date time range from which checkout forms will be taken.
        - name: sort
          in: query
          description: >-
            The results' sorting order. No prefix in the value means ascending order. `-` prefix means descending order.
            If you don't provide the sort parameter, the list is sorted by line item boughtAt date, descending.
          required: false
          schema:
            type: string
            enum:
              - lineItems.boughtAt
              - -lineItems.boughtAt
              - updatedAt
              - -updatedAt
      responses:
        '200':
          description: OK
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/CheckoutForms'
        '400':
          description: >
            Bad Request - Returned when request parameters contains illegal values.
        '401':
          description: Unauthorized
        '406':
          description: Not Acceptable
        '422':
          description: >
            Unprocessable Entity - Returned when limit or offset value is outside an acceptable range
      security:
      - bearer-token-for-user:
        - allegro:api:orders:read

  /order/checkout-forms/{id}:
    get:
      tags:
        - Order management
      summary: "Get an order's details"
      description: >-
        Use this resource to get an order details. Read more: <a href="../../tutorials/jak-obslugiwac-zamowienia-GRaj0qyvwtR#szczegoly-zamowienia" target="_blank">PL</a> / <a href="../../tutorials/process-orders-PgPMlWDr8Cv#order-details" target="_blank">EN</a>.
      operationId: getOrdersDetailsUsingGET
      parameters:
        - name: id
          in: path
          description: Checkout form identifier.
          required: true
          schema:
            type: string
            format: uuid
          example: "29738e61-7f6a-11e8-ac45-09db60ede9d6"
      responses:
        '200':
          description: OK
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/CheckoutForm'
        '401':
          description: Unauthorized
        '404':
          description: Not Found
        '406':
          description: Not Acceptable
        '422':
          description: >
            Unprocessable Entity - Returned when order id is malformed UUID.
      security:
      - bearer-token-for-user:
        - allegro:api:orders:read

  /order/carriers:
    get:
      tags:
        - Order management
      summary: "Get a list of available shipping carriers"
      description: >-
        Shipping carriers are essential to provide accurate tracking experience for customers.
        Use this resource to get a list of all available shipping carriers.


        The response of this resource can be stored in accordance with returned caching headers. Read more: <a href="../../news/nowy-zasob-do-pobrania-identyfikatorow-przewoznikow-8dmljjGRGUE" target="_blank">PL</a> / <a href="../../news/new-resource-to-retrieve-available-delivery-company-id-VL6zDDdr4hk" target="_blank">EN</a>.
      operationId: getOrdersCarriersUsingGET
      security:
        - bearer-token-for-application: []
        - bearer-token-for-user: []
      responses:
        '200':
          description: List of available shipping carriers.
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/OrdersShippingCarriersResponse'
              examples:
                order-carriers:
                  summary: Order carriers
                  value:
                    carriers:
                      - id: 'POCZTA_POLSKA'
                        name: 'Poczta Polska'
                      - id: 'DHL'
                        name: 'DHL'
                      - id: 'YUN_EXPRESS'
                        name: 'Yun Express'
                      - id: 'OTHER'
        '401':
          description: Unauthorized
        '404':
          description: Not Found

  '/order/checkout-forms/{id}/shipments':
    get:
      tags:
        - Order management
      summary: "Get a list of parcel tracking numbers"
      description: >-
        Get a list of parcel tracking numbers currently assigned to the order.
        Orders can be retrieved using REST API resource GET /order/checkout-forms.
        Please note that the shipment list may contain parcel tracking numbers added
        through other channels such as Moje Allegro or by the carrier that delivers the parcel. Read more: <a href="../../tutorials/jak-obslugiwac-zamowienia-GRaj0qyvwtR#jak-pobrac-numery-przesylek-dodane-do-zamowienia" target="_blank">PL</a> / <a href="../../tutorials/process-orders-PgPMlWDr8Cv#retrieving-tracking-numbers" target="_blank">EN</a>.
      operationId: getOrderShipmentsUsingGET
      parameters:
        - name: id
          in: path
          description: Order identifier.
          required: true
          schema:
            type: string
      responses:
        '200':
          description: >-
            Returns a list of parcel tracking numbers (shipments)
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/CheckoutFormOrderWaybillResponse'
        '401':
          description: Authentication failed, e.g. token is expired
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/AuthError'
        '404':
          description: Order not found or doesn't belong to the seller
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/ErrorsHolder'
      security:
      - bearer-token-for-user:
        - allegro:api:orders:read
    post:
      tags:
        - Order management
      summary: "Add a parcel tracking number"
      description: >-
        Add a parcel tracking number (shipment) to given order line items. Read more: <a href="../../tutorials/jak-obslugiwac-zamowienia-GRaj0qyvwtR#jak-dodac-numer-przesylki-do-przedmiotu-w-zamowieniu" target="_blank">PL</a> / <a href="../../tutorials/process-orders-PgPMlWDr8Cv#add-tracking-number-to-order" target="_blank">EN</a>.
      operationId: createOrderShipmentsUsingPOST
      parameters:
        - name: id
          in: path
          description: Order identifier.
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/vnd.allegro.public.v1+json:
            schema:
              $ref: '#/components/schemas/CheckoutFormAddWaybillRequest'
        description: request
        required: true
      responses:
        '201':
          description: The request is OK and the parcel tracking number will be assigned to the order
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/CheckoutFormAddWaybillCreated'
        '400':
          description: Missing required field or invalid value in the request (e.g. unknown carrier id, carrier name too long, invalid tracking number structure)
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/ErrorsHolder'
        '401':
          description: Authentication failed, e.g. token is expired
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/AuthError'
        '404':
          description: Order not found or doesn't belong to the seller
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/ErrorsHolder'
        '409':
          description: Maximum waybill usage exceeded (e.g. used in too many orders)
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/ErrorsHolder'
        '422':
          description: Some of the provided data is invalid, e.g. line item doesn't belong to the order
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/ErrorsHolder'
      security:
      - bearer-token-for-user:
        - allegro:api:orders:write

  /order/checkout-forms/{id}/fulfillment:
    put:
      tags:
        - Order management
      summary: "Set seller order status"
      description: >-
        Use to set seller order status. Read more: <a href="../../tutorials/jak-obslugiwac-zamowienia-GRaj0qyvwtR#zmiana-statusu-realizacji-zamowienia" target="_blank">PL</a> / <a href="../../tutorials/process-orders-PgPMlWDr8Cv#order-fulfillment-status-change" target="_blank">EN</a>.
      operationId: setOrderFulfillmentUsingPUT
      parameters:
        - name: id
          in: path
          description: Order identifier.
          required: true
          schema:
            type: string
        - name: checkoutForm.revision
          in: query
          description: Checkout form revision.
          required: false
          schema:
            type: string
          example: "819b5836"
      requestBody:
        content:
          application/vnd.allegro.public.v1+json:
            schema:
              $ref: '#/components/schemas/CheckoutFormFulfillment'
        description: request
        required: true
      responses:
        '204':
          description: Fulfillment set successfully
        '401':
          description: Authentication failed, e.g. token is expired
        '404':
          description: Order not found or doesn't belong to the seller
        '409':
          description: Conflict. Provided revision is outdated.
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/ErrorsHolder'
        '422':
          description: Some of the provided data is invalid, e.g. unrecognized status value or given status is not allowed to be set (e.g. `RETURNED` status).
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/ErrorsHolder'
      security:
      - bearer-token-for-user:
        - allegro:api:orders:write

  /order/checkout-forms/{id}/invoices:
    get:
      tags:
        - Order management
      summary: "Get order invoices details"
      description: 'Use to get invoices details including antivirus scan results and EPT invoice verification status. Read more: <a href="../../tutorials/jak-obslugiwac-zamowienia-GRaj0qyvwtR#jak-pobrac-informacje-o-fakturach-dodanych-do-zamowienia" target="_blank">PL</a> / <a href="../../tutorials/process-orders-PgPMlWDr8Cv#retrieve-information-about-invoices" target="_blank">EN</a>.'
      operationId: getOrderInvoicesDetails
      parameters:
        - name: id
          in: path
          description: Order identifier.
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Invoices fetched successfully
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/CheckoutFormsOrderInvoices'
        '403':
          description: You do not have permission to access this order
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/ErrorsHolder'
        '404':
          description: Order with given id does not exist
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/ErrorsHolder'
    post:
      tags:
        - Order management
      summary: "Post new invoice"
      description: 'Use to add new invoice metadata. Before you send an invoice file, you need to initialize the invoice instance with the required parameters. Read more: <a href="../../tutorials/jak-obslugiwac-zamowienia-GRaj0qyvwtR#jak-dodac-fakture-do-zamowienia" target="_blank">PL</a> / <a href="../../tutorials/process-orders-PgPMlWDr8Cv#add-an-invoice-to-the-order" target="_blank">EN</a>.'
      operationId: addOrderInvoicesMetadata
      parameters:
        - name: id
          in: path
          description: Order identifier.
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/vnd.allegro.public.v1+json:
            schema:
              $ref: '#/components/schemas/CheckFormsNewOrderInvoice'
        description: request
        required: true
      responses:
        '201':
          description: Invoice created successfully
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/CheckFormsNewOrderInvoiceId'
        '403':
          description: You do not have permission to access this order
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/ErrorsHolder'
        '404':
          description: Order with given id does not exist
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/ErrorsHolder'
        '409':
          description: Order with given id already has seller invoice
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/ErrorsHolder'
        '422':
          description: Some of the provided data is invalid or order does not allow an invoice or order's payment is rejected or previously uploaded file is still in scanning.
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/ErrorsHolder'
        '429':
          description: You're trying to add another metadata too fast. Upload a file to previously added metadata or wait a few seconds before adding next metadata.
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/ErrorsHolder'

  /order/checkout-forms/{id}/invoices/{invoiceId}/file:
    put:
      tags:
        - Order management
      summary: "Upload invoice file"
      description: 'Use to upload invoice file to match created invoice metadata. Read more: <a href="../../tutorials/jak-obslugiwac-zamowienia-GRaj0qyvwtR#jak-dodac-fakture-do-zamowienia" target="_blank">PL</a> / <a href="../../tutorials/process-orders-PgPMlWDr8Cv#add-an-invoice-to-the-order" target="_blank">EN</a>.'
      operationId: uploadOrderInvoiceFile
      parameters:
        - name: id
          in: path
          description: Order identifier.
          required: true
          schema:
            type: string
        - name: invoiceId
          in: path
          description: Invoice identifier.
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/pdf:
            schema:
              type: string
              format: binary
        required: true
      responses:
        '201':
          description: Invoice file uploaded successfully
        '403':
          description: You do not have permission to access this order
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/ErrorsHolder'
        '404':
          description: Order with given id does not exist or invoice with given id does not exist
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/ErrorsHolder'
        '409':
          description: Invoice already has a file
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/ErrorsHolder'
        '415':
          description: Unsupported media type
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/ErrorsHolder'
        '422':
          description: Some of the provided data is invalid or order does not allow an invoice or order's payment is rejected or previously uploaded file is still in scanning.
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/ErrorsHolder'
      security:
      - bearer-token-for-user:
        - allegro:api:orders:write

components:
  securitySchemes:
    bearer-token-for-user:
      type: oauth2
      description: >-
        **Important!** Do not require the user of your application to register a new instance of the application and send you Client_ID and Client_Secret. Regardless of the authorization method, the application must run on a single key (Client_ID). For more information <a href="../../tutorials/uwierzytelnianie-i-autoryzacja-zlq9e75GdIR#authorization-code-flow" target="_blank">PL</a> / <a href="../../tutorials/authentication-and-authorization-m09BlVyo7iY#authorization-code-flow" target="_blank">EN</a> to read about authorization code flow
        or <a href="../../tutorials/uwierzytelnianie-i-autoryzacja-zlq9e75GdIR#device-flow" target="_blank">PL</a> / <a href="../../tutorials/authentication-and-authorization-m09BlVyo7iY#device-flow" target="_blank">EN</a> to read about the device code flow.
      flows:
        authorizationCode:
          authorizationUrl: https://allegro.pl/auth/oauth/authorize
          tokenUrl: https://allegro.pl/auth/oauth/token
          scopes: {}
        x-deviceCode:
          authorizationUrl: https://allegro.pl/skojarz-aplikacje
          tokenUrl: https://allegro.pl/auth/oauth/token
          scopes: {}
    bearer-token-for-application:
      type: oauth2
      description: >-
        **Important!** Do not require the user of your application to register a new instance of the application and send you Client_ID and Client_Secret. Regardless of the authorization method, the application must run on a single key (Client_ID). For more information <a href="../../tutorials/uwierzytelnianie-i-autoryzacja-zlq9e75GdIR#client-credentials-flow" target="_blank">PL</a> / <a href="../../tutorials/authentication-and-authorization-m09BlVyo7iY#client-credentials-flow" target="_blank">EN</a> to read about client credentials flow.
      flows:
        clientCredentials:
          tokenUrl: https://allegro.pl/auth/oauth/token
          scopes: {}

  schemas:
    OrderEventStats:
      type: object
      properties:
        latestEvent:
          $ref: '#/components/schemas/LatestOrderEvent'

    LatestOrderEvent:
      type: object
      properties:
        id:
          type: string
          description: Latest event id.
        occurredAt:
          type: string
          format: date-time
          description: Latest event occurrence date.

    CheckoutForms:
      type: object
      properties:
        checkoutForms:
          type: array
          items:
            $ref: '#/components/schemas/CheckoutForm'
        count:
          type: integer
          description: Number of returned objects.
        totalCount:
          type: integer
          description: Total number of objects.

    CheckoutForm:
      type: object
      properties:
        id:
          type: string
          description: Checkout form id.
        messageToSeller:
          type: string
          description: Message from buyer to seller.
        buyer:
          $ref: '#/components/schemas/CheckoutFormBuyerReference'
        payment:
          $ref: '#/components/schemas/CheckoutFormPaymentReference'
        status:
          type: string
          description: Checkout form status.
          enum:
            - BOUGHT
            - FILLED_IN
            - READY_FOR_PROCESSING
            - CANCELLED
        fulfillment:
          $ref: '#/components/schemas/CheckoutFormFulfillment'
        delivery:
          $ref: '#/components/schemas/CheckoutFormDeliveryReference'
        invoice:
          $ref: '#/components/schemas/CheckoutFormInvoiceInfo'
        lineItems:
          type: array
          description: List of order items.
          items:
            $ref: '#/components/schemas/CheckoutFormLineItem'
        surcharges:
          type: array
          description: List of surcharges.
          items:
            $ref: '#/components/schemas/CheckoutFormSurcharge'
        discounts:
          type: array
          description: List of discounts.
          items:
            $ref: '#/components/schemas/CheckoutFormDiscount'
        summary:
          $ref: '#/components/schemas/CheckoutFormSummary'
        updatedAt:
          type: string
          format: date-time
          description: Checkout form last modification date.
        revision:
          type: string
          description: Checkout form revision.

    # Dodaj pozostałe schematy używane przez endpointy związane z zamówieniami
