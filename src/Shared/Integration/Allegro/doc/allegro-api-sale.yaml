openapi: 3.0.0
servers:
  - url: https://api.{environment}
    variables:
      environment:
        default: allegro.pl
        enum:
          - allegro.pl                    # Production server
          - allegro.pl.allegrosandbox.pl  # Sandbox server
info:
  description:
    'https://developer.allegro.pl/about


    Documentation is generated from [this OpenAPI 3.0 specification file](https://developer.allegro.pl/swagger.yaml).

    To start working with our API, you can also check our [official Allegro REST API public collection](https://www.postman.com/allegro-rest-api/allegro-rest-api/collection/4puh6ls/allegro-rest-api) in Postman.'
  version: 'latest'
  title: Allegro REST API - Sale
  termsOfService: 'https://developer.allegro.pl/rules/'
  contact:
    name: "API Support"
    url: 'https://github.com/allegro/allegro-api/issues'

tags:
  - name: Offer management
  - name: User's offer information

x-tagGroups:
  - name: Sale
    tags:
      - Offer management
      - User's offer information

security:
  - bearer-token-for-user: []

paths:
  '/sale/product-offers/{offerId}':
    patch:
      tags:
        - Offer management
      summary: "Edit an offer"
      description: "Use this resource to edit an offer. Read more: <a href=\"../../tutorials/jak-zarzadzac-ofertami-7GzB2L37ase#jak-edytowac-oferte\" target=\"_blank\">PL</a> / <a href=\"../../tutorials/how-to-process-list-of-offers-m09BKA5v8H3#how-to-edit-an-offer\" target=\"_blank\">EN</a>."
      operationId: modifyProductOffer
      parameters:
        - name: offerId
          in: path
          description: Offer identifier.
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/vnd.allegro.public.v1+json:
            schema:
              $ref: '#/components/schemas/ProductOfferPatchRequest'
        required: true
      responses:
        '200':
          description: OK
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/ProductOfferResponse'
        '202':
          description: Accepted
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/OfferProcessingStatus'
        '400':
          description: Bad request
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/ErrorsHolder'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/ErrorsHolder'
        '404':
          description: Not Found
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/ErrorsHolder'
        '422':
          description: Unprocessable Entity
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/ErrorsHolder'
      security:
        - bearer-token-for-user:
            - allegro:api:sale:offers:write
            
  '/sale/product-offers/{offerId}/parts':
    get:
      tags:
        - User's offer information
      summary: 'Get selected data of the particular product-offer'
      description: 'Use this resource to get selected data of the particular product-offer. Read more: <a href="../../tutorials/jak-zarzadzac-ofertami-7GzB2L37ase#jak-pobrac-wybrane-dane-oferty" target="_blank">PL</a> / <a href="../../tutorials/how-to-process-list-of-offers-m09BKA5v8H3#how-to-get-selected-offer-data" target="_blank">EN</a>.'
      operationId: getProductOfferParts
      parameters:
        - name: offerId
          in: path
          description: Offer identifier.
          required: true
          schema:
            type: string
        - name: parts
          in: query
          description: Parts of the offer to be returned.
          required: true
          schema:
            type: array
            items:
              type: string
              enum:
                - AFTER_SALES_SERVICES
                - ADDITIONAL_SERVICES
                - CONTACT
                - DELIVERY
                - DESCRIPTION
                - DISCOUNTS
                - IMAGES
                - LOCATION
                - PARAMETERS
                - PAYMENTS
                - PROMOTION
                - PUBLICATION
                - STOCK
      responses:
        '200':
          description: OK
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/ProductOfferPartsResponse'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/ErrorsHolder'
        '404':
          description: Not Found
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/ErrorsHolder'
      security:
        - bearer-token-for-user:
            - allegro:api:sale:offers:read
            
  '/sale/product-offers/{offerId}/operations/{operationId}':
    get:
      tags:
        - Offer management
      summary: 'Check the processing status of a POST or PATCH request'
      description: 'Use this resource to check the processing status of a POST or PATCH request. Read more: <a href="../../tutorials/jak-zarzadzac-ofertami-7GzB2L37ase#jak-sprawdzic-status-przetwarzania-oferty" target="_blank">PL</a> / <a href="../../tutorials/how-to-process-list-of-offers-m09BKA5v8H3#how-to-check-processing-status-of-an-offer" target="_blank">EN</a>.'
      operationId: getProductOfferOperation
      parameters:
        - name: offerId
          in: path
          description: Offer identifier.
          required: true
          schema:
            type: string
        - name: operationId
          in: path
          description: Operation identifier.
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/OfferProcessingStatus'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/ErrorsHolder'
        '404':
          description: Not Found
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/ErrorsHolder'
      security:
        - bearer-token-for-user:
            - allegro:api:sale:offers:read

components:
  securitySchemes:
    bearer-token-for-user:
      type: oauth2
      description: >-
        **Important!** Do not require the user of your application to register a new instance of the application and send you Client_ID and Client_Secret. Regardless of the authorization method, the application must run on a single key (Client_ID). For more information <a href="../../tutorials/uwierzytelnianie-i-autoryzacja-zlq9e75GdIR#authorization-code-flow" target="_blank">PL</a> / <a href="../../tutorials/authentication-and-authorization-m09BlVyo7iY#authorization-code-flow" target="_blank">EN</a> to read about authorization code flow
        or <a href="../../tutorials/uwierzytelnianie-i-autoryzacja-zlq9e75GdIR#device-flow" target="_blank">PL</a> / <a href="../../tutorials/authentication-and-authorization-m09BlVyo7iY#device-flow" target="_blank">EN</a> to read about the device code flow.
      flows:
        authorizationCode:
          authorizationUrl: https://allegro.pl/auth/oauth/authorize
          tokenUrl: https://allegro.pl/auth/oauth/token
          scopes: {}
        x-deviceCode:
          authorizationUrl: https://allegro.pl/skojarz-aplikacje
          tokenUrl: https://allegro.pl/auth/oauth/token
          scopes: {}
          
  schemas:
    ProductOfferPatchRequest:
      type: object
      properties:
        name:
          type: string
          description: Offer name.
        description:
          $ref: '#/components/schemas/Description'
        stock:
          $ref: '#/components/schemas/Stock'
        price:
          $ref: '#/components/schemas/Price'
        delivery:
          $ref: '#/components/schemas/Delivery'
        publication:
          $ref: '#/components/schemas/Publication'
        parameters:
          type: array
          description: List of offer parameters.
          items:
            $ref: '#/components/schemas/Parameter'
            
    Description:
      type: object
      properties:
        sections:
          type: array
          description: List of description sections.
          items:
            $ref: '#/components/schemas/DescriptionSection'
            
    DescriptionSection:
      type: object
      properties:
        items:
          type: array
          description: List of description items.
          items:
            $ref: '#/components/schemas/DescriptionItem'
            
    DescriptionItem:
      type: object
      properties:
        type:
          type: string
          description: Description item type.
          enum:
            - TEXT
            - IMAGE
            - HEADER
        content:
          type: string
          description: Description item content.
          
    Stock:
      type: object
      properties:
        available:
          type: integer
          description: Available stock.
        unit:
          type: string
          description: Stock unit.
          enum:
            - UNIT
            - PAIR
            - SET
            
    Price:
      type: object
      properties:
        amount:
          type: string
          description: The amount provided in a string format to avoid rounding errors.
        currency:
          type: string
          description: The currency provided as a 3-letter code in accordance with ISO 4217 standard (https://en.wikipedia.org/wiki/ISO_4217).
          
    Delivery:
      type: object
      properties:
        shippingRates:
          $ref: '#/components/schemas/ShippingRates'
        handlingTime:
          type: string
          description: Handling time.
          enum:
            - PT0S
            - PT24H
            - P2D
            - P3D
            - P4D
            - P5D
            - P7D
            - P10D
            - P14D
            - P21D
            - P30D
            - P60D
            
    ShippingRates:
      type: object
      properties:
        id:
          type: string
          description: Shipping rates ID.
          
    Publication:
      type: object
      properties:
        status:
          type: string
          description: Publication status.
          enum:
            - INACTIVE
            - ACTIVATING
            - ACTIVE
            - ENDED
        startingAt:
          type: string
          format: date-time
          description: Publication start date.
        endingAt:
          type: string
          format: date-time
          description: Publication end date.
          
    Parameter:
      type: object
      properties:
        id:
          type: string
          description: Parameter ID.
        valuesIds:
          type: array
          description: List of parameter value IDs.
          items:
            type: string
        values:
          type: array
          description: List of parameter values.
          items:
            type: string
        rangeValue:
          $ref: '#/components/schemas/ParameterRangeValue'
          
    ParameterRangeValue:
      type: object
      properties:
        from:
          type: string
          description: Range from value.
        to:
          type: string
          description: Range to value.
          
    ProductOfferResponse:
      type: object
      properties:
        id:
          type: string
          description: Offer ID.
        name:
          type: string
          description: Offer name.
        category:
          $ref: '#/components/schemas/Category'
        primaryImage:
          $ref: '#/components/schemas/Image'
        sellingMode:
          $ref: '#/components/schemas/SellingMode'
        stock:
          $ref: '#/components/schemas/Stock'
        publication:
          $ref: '#/components/schemas/Publication'
        delivery:
          $ref: '#/components/schemas/Delivery'
        
    Category:
      type: object
      properties:
        id:
          type: string
          description: Category ID.
        name:
          type: string
          description: Category name.
          
    Image:
      type: object
      properties:
        url:
          type: string
          description: Image URL.
          
    SellingMode:
      type: object
      properties:
        format:
          type: string
          description: Selling format.
          enum:
            - BUY_NOW
            - AUCTION
            - ADVERTISEMENT
        price:
          $ref: '#/components/schemas/Price'
        
    ProductOfferPartsResponse:
      type: object
      properties:
        id:
          type: string
          description: Offer ID.
        name:
          type: string
          description: Offer name.
        description:
          $ref: '#/components/schemas/Description'
        stock:
          $ref: '#/components/schemas/Stock'
        price:
          $ref: '#/components/schemas/Price'
        delivery:
          $ref: '#/components/schemas/Delivery'
        publication:
          $ref: '#/components/schemas/Publication'
        parameters:
          type: array
          description: List of offer parameters.
          items:
            $ref: '#/components/schemas/Parameter'
            
    OfferProcessingStatus:
      type: object
      properties:
        id:
          type: string
          description: Operation ID.
        status:
          type: string
          description: Operation status.
          enum:
            - PENDING
            - PROCESSING
            - COMPLETED
            - FAILED
        errors:
          type: array
          description: List of errors.
          items:
            $ref: '#/components/schemas/Error'
            
    ErrorsHolder:
      type: object
      properties:
        errors:
          type: array
          description: List of errors.
          items:
            $ref: '#/components/schemas/Error'
            
    Error:
      type: object
      properties:
        code:
          type: string
          description: Error code.
        message:
          type: string
          description: Error message.
        path:
          type: string
          description: Error path.
