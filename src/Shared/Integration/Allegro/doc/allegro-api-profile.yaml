openapi: 3.0.0
servers:
  - url: https://api.{environment}
    variables:
      environment:
        default: allegro.pl
        enum:
          - allegro.pl                    # Production server
          - allegro.pl.allegrosandbox.pl  # Sandbox server
info:
  description:
    'https://developer.allegro.pl/about


    Documentation is generated from [this OpenAPI 3.0 specification file](https://developer.allegro.pl/swagger.yaml).

    To start working with our API, you can also check our [official Allegro REST API public collection](https://www.postman.com/allegro-rest-api/allegro-rest-api/collection/4puh6ls/allegro-rest-api) in Postman.'
  version: 'latest'
  title: Allegro REST API - Profile
  termsOfService: 'https://developer.allegro.pl/rules/'
  contact:
    name: "API Support"
    url: 'https://github.com/allegro/allegro-api/issues'

tags:
  - name: Information about user
  - name: Additional services

x-tagGroups:
  - name: Profile
    tags:
      - Information about user
      - Additional services

security:
  - bearer-token-for-user: []

paths:
  /me:
    get:
      tags:
        - Information about user
      summary: "Get basic information about user"
      description: "Use this resource to get basic information about user."
      operationId: meGET
      responses:
        '200':
          description: OK
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/MeResponse'
        '401':
          description: Unauthorized
      security:
        - bearer-token-for-user:
            - allegro:api:profile:read
            
  /sale/user-ratings:
    get:
      tags:
        - Information about user
      summary: "Get the user's ratings"
      description: "Use this resource to get information about ratings received. Read more: <a href=\"../../tutorials/jak-sprawdzic-oceny-otrzymane-przez-uzytkownika-D7Kj9gw4xFA\" target=\"_blank\">PL</a> / <a href=\"../../tutorials/how-to-check-user-s-ratings-D7Kj9M71Bu6\" target=\"_blank\">EN</a>."
      operationId: userRatingGET
      parameters:
        - name: recommended
          in: query
          description: Filter by recommended.
          required: false
          schema:
            type: string
            enum:
              - 'true'
              - 'false'
        - name: offset
          in: query
          description: The offset of returned ratings.
          required: false
          schema:
            type: integer
            default: 0
        - name: limit
          in: query
          description: The limit of returned ratings.
          required: false
          schema:
            type: integer
            default: 20
            maximum: 100
      responses:
        '200':
          description: OK
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/UserRatingListResponse'
        '401':
          description: Unauthorized
        '404':
          description: Not Found
      security:
        - bearer-token-for-user:
            - allegro:api:profile:read
            
  /sale/offer-additional-services/groups:
    post:
      tags:
        - Additional services
      summary: 'Create additional services group'
      description: 'Use this resource to create a group of additional services. Read more: <a href="../../tutorials/jak-zarzadzac-ofertami-7GzB2L37ase#jak-dodac-uslugi-dodatkowe-do-oferty" target="_blank">PL</a> / <a href="../../tutorials/how-to-process-list-of-offers-m09BKA5v8H3#how-to-add-additional-services-to-an-offer" target="_blank">EN</a>.'
      operationId: createAdditionalServicesGroup
      requestBody:
        content:
          application/vnd.allegro.public.v1+json:
            schema:
              $ref: '#/components/schemas/AdditionalServicesGroupRequest'
        required: true
      responses:
        '201':
          description: Created
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/AdditionalServicesGroupResponse'
        '400':
          description: Bad request
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/ErrorsHolder'
        '401':
          description: Unauthorized
        '422':
          description: Unprocessable Entity
          content:
            application/vnd.allegro.public.v1+json:
              schema:
                $ref: '#/components/schemas/ErrorsHolder'
      security:
        - bearer-token-for-user:
            - allegro:api:profile:read

components:
  securitySchemes:
    bearer-token-for-user:
      type: oauth2
      description: >-
        **Important!** Do not require the user of your application to register a new instance of the application and send you Client_ID and Client_Secret. Regardless of the authorization method, the application must run on a single key (Client_ID). For more information <a href="../../tutorials/uwierzytelnianie-i-autoryzacja-zlq9e75GdIR#authorization-code-flow" target="_blank">PL</a> / <a href="../../tutorials/authentication-and-authorization-m09BlVyo7iY#authorization-code-flow" target="_blank">EN</a> to read about authorization code flow
        or <a href="../../tutorials/uwierzytelnianie-i-autoryzacja-zlq9e75GdIR#device-flow" target="_blank">PL</a> / <a href="../../tutorials/authentication-and-authorization-m09BlVyo7iY#device-flow" target="_blank">EN</a> to read about the device code flow.
      flows:
        authorizationCode:
          authorizationUrl: https://allegro.pl/auth/oauth/authorize
          tokenUrl: https://allegro.pl/auth/oauth/token
          scopes: {}
        x-deviceCode:
          authorizationUrl: https://allegro.pl/skojarz-aplikacje
          tokenUrl: https://allegro.pl/auth/oauth/token
          scopes: {}
          
  schemas:
    MeResponse:
      type: object
      properties:
        id:
          type: string
          description: User ID.
        login:
          type: string
          description: User login.
        firstName:
          type: string
          description: User first name.
        lastName:
          type: string
          description: User last name.
        email:
          type: string
          description: User email.
          
    UserRatingListResponse:
      type: object
      properties:
        ratings:
          type: array
          items:
            $ref: '#/components/schemas/UserRating'
        count:
          type: integer
          description: Number of returned objects.
        totalCount:
          type: integer
          description: Total number of objects.
          
    UserRating:
      type: object
      properties:
        id:
          type: string
          description: Rating ID.
        recommended:
          type: boolean
          description: Whether the rating is recommended.
        comment:
          type: string
          description: Rating comment.
        createdAt:
          type: string
          format: date-time
          description: Rating creation date.
        order:
          $ref: '#/components/schemas/Order'
        
    Order:
      type: object
      properties:
        id:
          type: string
          description: Order ID.
        offer:
          $ref: '#/components/schemas/Offer'
          
    Offer:
      type: object
      properties:
        id:
          type: string
          description: Offer ID.
        name:
          type: string
          description: Offer name.
          
    AdditionalServicesGroupRequest:
      type: object
      properties:
        name:
          type: string
          description: Additional services group name.
        additionalServices:
          type: array
          items:
            $ref: '#/components/schemas/AdditionalServiceRequest'
            
    AdditionalServiceRequest:
      type: object
      properties:
        id:
          type: string
          description: Additional service ID.
        name:
          type: string
          description: Additional service name.
        price:
          $ref: '#/components/schemas/Price'
          
    AdditionalServicesGroupResponse:
      type: object
      properties:
        id:
          type: string
          description: Additional services group ID.
        name:
          type: string
          description: Additional services group name.
        additionalServices:
          type: array
          items:
            $ref: '#/components/schemas/AdditionalServiceResponse'
            
    AdditionalServiceResponse:
      type: object
      properties:
        id:
          type: string
          description: Additional service ID.
        name:
          type: string
          description: Additional service name.
        price:
          $ref: '#/components/schemas/Price'
          
    Price:
      type: object
      properties:
        amount:
          type: string
          description: The amount provided in a string format to avoid rounding errors.
        currency:
          type: string
          description: The currency provided as a 3-letter code in accordance with ISO 4217 standard (https://en.wikipedia.org/wiki/ISO_4217).
          
    ErrorsHolder:
      type: object
      properties:
        errors:
          type: array
          description: List of errors.
          items:
            $ref: '#/components/schemas/Error'
            
    Error:
      type: object
      properties:
        code:
          type: string
          description: Error code.
        message:
          type: string
          description: Error message.
        path:
          type: string
          description: Error path.
