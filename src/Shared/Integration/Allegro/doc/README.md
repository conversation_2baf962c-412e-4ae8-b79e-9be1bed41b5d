# Allegro API Documentation

## Struktura plików

W tym katalogu znajdują się pliki dokumentacji API Allegro w formacie OpenAPI (YAML).

### Plik główny

- `allegro-api.yaml` - kompletna dokumentacja API Allegro zawierająca wszystkie endpointy i definicje

### Pliki podzielone według scope'ów autoryzacyjnych

Dla ułatwienia pracy z API, dokumentacja została podzielona na mniejsze pliki według scope'ów autoryzacyjnych:

- `allegro-api-orders.yaml` - endpointy związane z zamówieniami (scope'y: `allegro:api:orders:read`, `allegro:api:orders:write`)
- `allegro-api-billing.yaml` - endpointy związane z rozliczeniami (scope: `allegro:api:billing:read`)
- `allegro-api-fulfillment.yaml` - endpointy związane z realizacją zamówień (scope'y: `allegro:api:fulfillment:read`, `allegro:api:fulfillment:write`)
- `allegro-api-payments.yaml` - endpointy związane z płatnościami (scope'y: `allegro:api:payments:read`, `allegro:api:payments:write`)
- `allegro-api-profile.yaml` - endpointy związane z profilem użytkownika (scope'y: `allegro:api:profile:read`, `allegro:api:profile:write`)
- `allegro-api-sale.yaml` - endpointy związane ze sprzedażą (scope'y: `allegro:api:sale:offers`, `allegro:api:sale:settings`)
- `allegro-api-shipments.yaml` - endpointy związane z przesyłkami (scope'y: `allegro:api:shipments:read`, `allegro:api:shipments:write`)

## Korzystanie z dokumentacji

Każdy plik zawiera:
- Podstawowe informacje o API (openapi, servers, info, security)
- Tagi związane z danym scope'em
- Endpointy używające danego scope'a
- Schematy używane przez te endpointy

Możesz korzystać z pliku głównego `allegro-api.yaml`, jeśli potrzebujesz dostępu do całego API, lub z mniejszych plików, jeśli pracujesz tylko z określonymi funkcjonalnościami.

## Aktualizacja dokumentacji

Przy aktualizacji API należy zaktualizować zarówno plik główny, jak i odpowiednie pliki podzielone według scope'ów.
