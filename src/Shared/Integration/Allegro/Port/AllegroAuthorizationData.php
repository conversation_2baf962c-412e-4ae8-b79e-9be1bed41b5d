<?php

declare(strict_types=1);

namespace Shared\Integration\Allegro\Port;

use Shared\Integration\Abstract\Port\Authorization\AuthorizationDataAbstract;

class AllegroAuthorizationData extends AuthorizationDataAbstract
{
    private const string redirect_url = 'redirect_url';
    private const string access_token = 'access_token';
    private const string token_type = 'token_type';
    private const string refresh_token = 'refresh_token';
    private const string expires_in = 'expires_in';
    private const string scope = 'scope';
    private const string allegro_api = 'allegro_api';
    private const string iss = 'iss';
    private const string jti = 'jti';

    public function __construct(
        public readonly string $redirectUrl,
        public readonly string $accessToken,
        public readonly string $tokenType,
        public readonly string $refreshToken,
        public readonly int $expiredIn,
        public readonly string $scope,
        public readonly bool $allegroApi,
        public readonly string $iss,
        public readonly string $jti
    ) {
    }

    public function toArray(): array
    {
        return [
            self::redirect_url => $this->redirectUrl,
            self::access_token => $this->accessToken,
            self::token_type => $this->tokenType,
            self::refresh_token => $this->refreshToken,
            self::expires_in => $this->expiredIn,
            self::scope => $this->scope,
            self::allegro_api => $this->allegroApi,
            self::iss => $this->iss,
            self::jti => $this->jti,
        ];
    }

    public static function fromArray(array $data): static
    {
        return new static(
            $data[self::redirect_url],
            $data[self::access_token],
            $data[self::token_type],
            $data[self::refresh_token],
            $data[self::expires_in],
            $data[self::scope],
            $data[self::allegro_api],
            $data[self::iss],
            $data[self::jti],
        );
    }
}
