<?php

declare(strict_types=1);

namespace Shared\Integration\Allegro\Port;

use Shared\Integration\Abstract\Port\Authorization\AuthorizationAbstract;
use Shared\Integration\Abstract\Port\IntegrationInterface;
use Shared\Integration\Allegro\Adapter\AllegroAuthorization;

readonly class AllegroIntegration implements IntegrationInterface
{
    public function __construct(
        private AllegroAuthorization $authorization
    ) {
    }

    public function getName(): string
    {
        return 'Allegro';
    }

    public function getDescription(): string
    {
        return '';
    }

    public function authorizator(): AuthorizationAbstract
    {
        return $this->authorization;
    }
}
