<?php

declare(strict_types=1);

namespace Shared\Integration\Abstract\Port\Authorization\Method;

use Shared\Integration\Abstract\Port\Authorization\AuthorizationDataAbstract;

interface OAuthCodeFlowAbstractAuthorizationInterface extends AbstractAuthorizationMethodInterface
{
    public function authorizationUrl(string $redirectUrl): string;

    public function exchangeCode(
        string $code,
        string $redirectUrl
    ): AuthorizationDataAbstract;

    public function refreshToken(
        array $authorizeData,
    ): AuthorizationDataAbstract;
}
