<?php

declare(strict_types=1);

namespace Shared\Domain;

use Exception;
use Illuminate\Database\Eloquent\Model;
use Shared\Error\ContextExceptionInterface;
use Shared\Error\ContextExceptionTrait;

class OptimisticLockingException extends Exception implements ContextExceptionInterface
{
    use ContextExceptionTrait;

    public function __construct(
        protected readonly ?Model $model = null,
        protected readonly ?string $notification = null,
        protected readonly ?int $version = null,
        string $message = 'The record has been modified since it was loaded.',
        int $code = 409,
        ?\Throwable $previous = null
    ) {
        parent::__construct($message, $code, $previous);

        if ($version !== null) {
            $this->addContext('requested_version', $version);
        }

        if ($model !== null) {
            $this->addContext('model_version', $model->version ?? null);
            $this->addContext('model_class', get_class($model));
            $this->addContext('model_id', $model->getKey());
        }

        if ($notification !== null) {
            $this->addContext('notification', $notification);
        }
    }
}
