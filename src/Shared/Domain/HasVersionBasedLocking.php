<?php

declare(strict_types=1);

namespace Shared\Domain;

use Illuminate\Database\Eloquent\Model;

/**
 * Trait HasVersionBasedLocking
 *
 * Provides optimistic locking functionality for Eloquent models based on a version number.
 * This approach is more explicit than timestamp-based locking and works better with aggregates.
 *
 * Usage:
 * 1. Add this trait to your model
 * 2. Add a 'version' column to your table (integer, default 0)
 * 3. Use the scopeWithVersion method in your queries
 * 4. Use the updateWithVersion method to update records with optimistic locking
 */
trait HasVersionBasedLocking
{
    public static function bootHasVersionBasedLocking(): void
    {
        static::creating(function (Model $model) {
            $model->attributes['version'] = 1;
        });
    }

    /**
     * @throws OptimisticLockingException
     */
    public function lock(callable $cb, ?int $version = null, ?string $notification = null)
    {
        $notification ??= __d('database', 'Ktoś Cię wyprzedził i zminił dane - odświes stronę zeby załadować aktualne dane');
        $version ??= $this->getVersion();

        return $this->getConnection()->transaction(function() use ($version, $notification, $cb) {
            $this->attributes['version'] = $version + 1;
            $result = $this->where('version', $version)->update(['version' => $version + 1]);

            if(!$result) {
                throw new OptimisticLockingException(
                    model: $this,
                    notification: $notification,
                    version: $version
                );
            }

            return $cb();
        });
    }

    /**
     * @throws OptimisticLockingException
     */
    public function optimisticPush(?int $version, ?string $notification = null): void
    {
        $this->lock(fn() => $this->push(), $version, $notification);
    }

    public function getVersion(): int
    {
        if (!$this->exists || !isset($this->attributes['version'])) {
            return 0;
        }

        return (int)$this->attributes['version'];
    }
}
