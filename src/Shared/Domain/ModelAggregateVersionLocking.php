<?php

declare(strict_types=1);

namespace Shared\Domain;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Trait ModelAggregateVersionLocking
 *
 * Provides version-based locking for models that belong to an aggregate root.
 * Instead of maintaining their own version, these models use the version from their aggregate root.
 *
 * Usage:
 * 1. Add this trait to your model
 * 2. Implement the aggregateRoot() method to return the BelongsTo relation to the aggregate root
 * 3. The aggregate root should use the HasVersionBasedLocking trait
 */
trait ModelAggregateVersionLocking
{
    /**
     * Returns the BelongsTo relation to the aggregate root.
     * This method must be implemented by the model using this trait.
     */
    abstract public function aggregateRoot(): BelongsTo;

    /**
     * Get the version from the aggregate root.
     *
     * @return int
     */
    public function getVersionAttribute(): ?int
    {
        return $this->aggregateRoot?->version;
    }

    /**
     * Lock the aggregate root and execute the callback.
     *
     * @param callable $cb
     * @param int|null $version
     * @param string|null $notification
     * @return mixed
     * @throws OptimisticLockingException
     */
    public function lock(callable $cb, ?int $version = null, ?string $notification = null)
    {
        $aggregateRoot = $this->aggregateRoot;

        if (!$aggregateRoot || !method_exists($aggregateRoot, 'lock')) {
            throw new \RuntimeException('Aggregate root does not support locking');
        }

        return $aggregateRoot->lock($cb, $version, $notification);
    }

    /**
     * Optimistically push changes to the database.
     *
     * @param int|null $version
     * @param string|null $notification
     * @throws OptimisticLockingException
     */
    public function optimisticPush(?int $version, ?string $notification = null): void
    {
        $this->lock(fn() => $this->push(), $version, $notification);
    }
}
