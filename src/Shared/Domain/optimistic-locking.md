# Optimistic Locking in the Application

## Introduction

Optimistic locking is a concurrency control method that allows multiple users to access and potentially update the same record without blocking each other. Unlike pessimistic locking, which locks the record before editing, optimistic locking allows parallel editing but verifies that the data hasn't changed before saving.

## Implementation

In the Salesto application, we've implemented optimistic locking using the `HasOptimisticLocking` trait, which can be added to any Eloquent model.

### How It Works

The trait uses the `updated_at` column as a version marker. When updating a record, it checks if the `updated_at` value hasn't changed since the record was loaded. If it has changed, it means someone else has updated the record in the meantime, and an `OptimisticLockingException` will be thrown.

### Using the Trait

1. Add the trait to your model:

```php
use Shared\Domain\HasOptimisticLocking;

class YourModel extends Model
{
    use HasOptimisticLocking;

    // rest of the model
}
```

2. Use the `updateWithOptimisticLock` method to update a record:

```php
try {
    $model->updateWithOptimisticLock([
        'field1' => 'value1',
        'field2' => 'value2',
    ], $model->getOptimisticLockSignature());

    // Update successful
} catch (OptimisticLockingException $e) {
    // Handle conflict
    // e.g., display a message to the user
}
```

3. You can use the `getOptimisticLockSignature()` method to get the current version signature:

```php
$lockSignature = $model->getOptimisticLockSignature();
```

4. You can also use the `withOptimisticLock` scope in queries:

```php
$result = YourModel::query()
    ->where('id', $id)
    ->withOptimisticLock($model->getOptimisticLockSignature())
    ->update($attributes);

if ($result === 0) {
    // Handle conflict
}
```

### Exception Handling

The `OptimisticLockingException` is handled globally in `App\Exceptions\Handler`. The handling depends on the request type:

- For API requests (JSON) - HTTP 409 Conflict code is returned
- For Filament panel - a notification is displayed and the user is redirected back
- For regular web requests - an error is added to the session and the user is redirected back

## Implementation Example

Example usage in the `Order` model to update an order item:

```php
public function updateItem(
    string $itemId,
    string $name,
    string|Money $price,
    int $quantity,
    ?string $sku = null,
    ?array $attributes = null,
    ?string $updatedAt = null
): OrderItem
{
    // Convert price
    $moneyPrice = Money::create($price);
    $priceAmount = $moneyPrice->getAmount();

    // Calculate total
    $total = MoneyCalculator::multiply($priceAmount, (string)$quantity);

    // Update data
    $updateData = [
        'name' => $name,
        'price' => $priceAmount,
        'quantity' => $quantity,
        'total' => $total,
        'sku' => $sku,
        'attributes' => $attributes,
    ];

    // Find the item
    $item = $this->items()->where('id', $itemId)->firstOrFail();

    // Update with optimistic locking
    // If $updatedAt is provided, use it, otherwise use the current signature
    $lockSignature = $updatedAt ?? $item->getOptimisticLockSignature();
    $item->updateWithOptimisticLock($updateData, $lockSignature);

    return $item;
}
```

## Benefits

1. **Performance** - no database-level locks
2. **Simplicity** - easy to understand and implement
3. **Consistency** - uniform approach throughout the application
4. **Flexibility** - can be easily enabled/disabled for individual models
5. **UX** - better user experience through clear conflict messages
