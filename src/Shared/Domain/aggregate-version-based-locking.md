# Aggregate Root Version-Based Optimistic Locking

## Introduction

In Domain-Driven Design (DDD), an Aggregate Root is a cluster of domain objects that can be treated as a single unit. When implementing optimistic locking in a DDD context, it's important to lock at the aggregate root level rather than at the individual entity level. This ensures that the entire aggregate is consistent and that concurrent modifications are properly detected.

## Implementation in Salesto

In the Salesto application, we've implemented version-based optimistic locking at the aggregate root level using the `HasVersionBasedLocking` trait. This approach ensures that any changes to entities within an aggregate are properly tracked and that concurrent modifications are detected.

### How It Works

1. The aggregate root (e.g., `Order`) has a `version` column that is incremented with each change to the aggregate or any of its entities.
2. When updating an entity within the aggregate (e.g., `OrderItem`), the version of the aggregate root is checked and incremented.
3. If the version has changed since the aggregate was loaded, an `OptimisticLockingException` is thrown.
4. All updates to entities within the aggregate are performed within a database transaction to ensure consistency.

### Benefits of Aggregate Root Locking

1. **Consistency**: The entire aggregate is treated as a single unit, ensuring that all entities within it are consistent.
2. **Simplicity**: Only one version number needs to be tracked and checked, regardless of how many entities are in the aggregate.
3. **Correctness**: This approach aligns with DDD principles, where the aggregate root is responsible for maintaining the invariants of the entire aggregate.
4. **Performance**: No need to check and update version numbers for each entity within the aggregate.

## Example: Updating an OrderItem

```php
public function updateItem(
    string $itemId,
    string $name,
    string|Money $price,
    int $quantity,
    ?string $sku = null,
    ?array $attributes = null,
    ?int $version = null
): OrderItem
{
    // Prepare the update data
    $updateData = [
        'name' => $name,
        'price' => $priceAmount,
        'quantity' => $quantity,
        'total' => $total,
        'sku' => $sku,
        'attributes' => $attributes,
    ];

    // Find the item
    $item = $this->items()->where('id', $itemId)->firstOrFail();

    // Get the current version of the aggregate root
    $currentVersion = $version ?? $this->getVersion();

    // Start a database transaction
    return \DB::transaction(function () use ($item, $updateData, $currentVersion) {
        // First check and update the aggregate root version
        $updated = $this->withVersion($currentVersion)->update([
            'version' => $currentVersion + 1
        ]);

        if (!$updated) {
            throw new OptimisticLockingException(
                'The order has been modified since it was loaded.',
                $this
            );
        }

        // Now update the item (without optimistic locking at the entity level)
        $item->update($updateData);

        // Refresh the models
        $item->refresh();
        $this->refresh();

        return $item;
    });
}
```

## Usage in Filament Forms

When using Filament forms to update entities within an aggregate, you need to:

1. Include a hidden field for the aggregate root's version:

```php
Hidden::make('version')
    ->default(fn (Form $form) => $form->getRecord()?->order?->getVersion()),
```

2. Pass the version to the update method:

```php
->using(function (OrderItem $record, array $data, RelationManager $livewire): mixed {
    return $livewire->getOwnerRecord()->updateItem(
        itemId: $record->id,
        name: $data['name'],
        price: $data['price'],
        quantity: (int) $data['quantity'],
        sku: $data['sku'] ?? null,
        attributes: $data['attributes'] ?? null,
        version: isset($data['version']) ? (int)$data['version'] : $record->order->getVersion()
    );
}),
```

## Exception Handling

The `OptimisticLockingException` is handled globally in `App\Exceptions\Handler`. The handling depends on the request type:

- For API requests (JSON) - HTTP 409 Conflict code is returned
- For Filament panel - a notification is displayed and the user is redirected back
- For regular web requests - an error is added to the session and the user is redirected back

## Conclusion

Version-based optimistic locking at the aggregate root level is a powerful technique for ensuring consistency in a DDD context. By tracking changes at the aggregate level rather than at the individual entity level, we can ensure that the entire aggregate is consistent and that concurrent modifications are properly detected.
