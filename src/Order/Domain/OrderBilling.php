<?php

declare(strict_types=1);

namespace Order\Domain;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;

/**
 * 
 *
 * @Entity 
 * @property string $id
 * @property string $order_id
 * @property string $company_name
 * @property string $tax_id
 * @property string|null $company_address
 * @property int $version
 * @property Carbon|null $deleted_at
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property-read Order $order
 * @method static \Illuminate\Database\Eloquent\Builder|OrderBilling newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|OrderBilling newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|OrderBilling query()
 * @method static \Illuminate\Database\Eloquent\Builder|OrderBilling whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderBilling whereOrderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderBilling whereCompanyName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderBilling whereTaxId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderBilling whereCompanyAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderBilling whereVersion($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderBilling onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderBilling whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderBilling whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderBilling whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderBilling withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderBilling withoutTrashed()
 * @mixin \Eloquent
 */
class OrderBilling extends Model
{
    use SoftDeletes, HasUuids;

    protected $table = 'purchase.order_billings';

    protected $fillable = [
        'company_name',
        'company_address',
        'tax_id',
        'version',
    ];

    protected $casts = [
        'version' => 'integer',
    ];

    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }
}
