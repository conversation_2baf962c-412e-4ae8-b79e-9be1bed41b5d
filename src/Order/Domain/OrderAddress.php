<?php

declare(strict_types=1);

namespace Order\Domain;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;

/**
 * 
 *
 * @Entity 
 * @property string $id
 * @property string $order_id
 * @property string $address
 * @property string $city
 * @property string $postal_code
 * @property string $country
 * @property string|null $state
 * @property int $version
 * @property Carbon|null $deleted_at
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property-read Order $order
 * @property-read string $full_address
 * @method static \Illuminate\Database\Eloquent\Builder|OrderAddress newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|OrderAddress newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|OrderAddress query()
 * @method static \Illuminate\Database\Eloquent\Builder|OrderAddress whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderAddress whereOrderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderAddress whereAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderAddress whereCity($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderAddress wherePostalCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderAddress whereCountry($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderAddress whereState($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderAddress whereVersion($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderAddress onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderAddress whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderAddress whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderAddress whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderAddress withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderAddress withoutTrashed()
 * @mixin \Eloquent
 */
class OrderAddress extends Model
{
    use SoftDeletes, HasUuids;

    protected $table = 'purchase.order_addresses';

    protected $fillable = [
        'address',
        'city',
        'postal_code',
        'country',
        'state',
        'version',
    ];

    protected $casts = [
        'version' => 'integer',
    ];

    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    public function getFullAddressAttribute(): string
    {
        $address = "{$this->address}, {$this->postal_code} {$this->city}";

        if ($this->state) {
            $address .= ", {$this->state}";
        }

        $address .= ", {$this->country}";

        return $address;
    }
}
