<?php

declare(strict_types=1);

namespace Order\Domain;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;

/**
 * 
 *
 * @Entity 
 * @property string $id
 * @property string $order_id
 * @property string $first_name
 * @property string $last_name
 * @property string|null $email
 * @property string|null $phone
 * @property int $version
 * @property Carbon|null $deleted_at
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property-read Order $order
 * @property-read string $full_name
 * @method static \Illuminate\Database\Eloquent\Builder|OrderBuyer newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|OrderBuyer newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|OrderBuyer query()
 * @method static \Illuminate\Database\Eloquent\Builder|OrderBuyer whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderBuyer whereOrderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderBuyer whereFirstName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderBuyer whereLastName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderBuyer whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderBuyer wherePhone($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderBuyer whereVersion($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderBuyer onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderBuyer whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderBuyer whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderBuyer whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderBuyer withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderBuyer withoutTrashed()
 * @mixin \Eloquent
 */
class OrderBuyer extends Model
{
    use SoftDeletes, HasUuids;

    protected $table = 'purchase.order_buyers';

    protected $fillable = [
        'first_name',
        'last_name',
        'email',
        'phone',
        'version',
    ];

    protected $casts = [
        'version' => 'integer',
    ];

    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    public function getFullNameAttribute(): string
    {
        return "{$this->first_name} {$this->last_name}";
    }


}
