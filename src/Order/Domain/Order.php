<?php

declare(strict_types=1);

namespace Order\Domain;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Shared\Domain\HasVersionBasedLocking;
use Shared\Money\Money;
use Shared\Money\MoneyCalculator;

/**
 * 
 *
 * @AggregateRoot 
 * @property string $id
 * @property string $currency
 * @property string|null $notes
 * @property Carbon|null $deleted_at
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property-read Collection<int, OrderItem> $items
 * @property-read Collection<int, OrderBuyer> $buyers
 * @property-read Collection<int, OrderAddress> $addresses
 * @property-read Collection<int, OrderBilling> $billings
 * @property-read OrderItem|null $item
 * @property-read OrderBuyer|null $buyer
 * @property-read OrderAddress|null $address
 * @property-read OrderBilling|null $billing
 * @method static \Illuminate\Database\Eloquent\Builder|Order newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Order newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Order query()
 * @method static \Illuminate\Database\Eloquent\Builder|Order whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Order whereNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Order whereNotes($value)
 * @property int $version Version number for optimistic locking
 * @property-read int|null $addresses_count
 * @property-read int|null $billings_count
 * @property-read int|null $buyers_count
 * @property-read int|null $items_count
 * @method static \Database\Factories\Order\Domain\OrderFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Order onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Order whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Order whereCurrency($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Order whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Order whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Order whereVersion($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Order withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Order withoutTrashed()
 * @mixin \Eloquent
 */
class Order extends Model
{
    use SoftDeletes, HasUuids, HasFactory, HasVersionBasedLocking;

    protected $table = 'purchase.orders';

    protected $fillable = [
        'currency',
        'notes',
        'version',
    ];

    protected $casts = [
        'version' => 'integer',
    ];

    public function items(): HasMany
    {
        return $this->hasMany(OrderItem::class);
    }

    public function item(): HasOne
    {
        return $this->hasOne(OrderItem::class)->latest();
    }

    public function buyers(): HasMany
    {
        return $this->hasMany(OrderBuyer::class);
    }

    public function buyer(): HasOne
    {
        return $this->hasOne(OrderBuyer::class)->latest();
    }

    public function addresses(): HasMany
    {
        return $this->hasMany(OrderAddress::class);
    }

    public function address(): HasOne
    {
        return $this->hasOne(OrderAddress::class)->latest();
    }

    public function billings(): HasMany
    {
        return $this->hasMany(OrderBilling::class);
    }

    public function billing(): HasOne
    {
        return $this->hasOne(OrderBilling::class)->latest();
    }


    public function addItem(
        string $name,
        string|Money $price = '0',
        int $quantity = 1,
        ?string $sku = null,
        ?array $attributes = null,
        ?string $id = null
    ): OrderItem {
        $itemData = [
            'name' => $name,
            'price' => Money::create($price),
            'quantity' => $quantity,
            'sku' => $sku,
            'attributes' => $attributes,
        ];

        if ($id) {
            $itemData['id'] = $id;
        }

        return $this->lock(fn() => $this->items()->create($itemData));
    }

    public function updateBuyer(
        string $firstName,
        string $lastName,
        ?string $email = null,
        ?string $phone = null
    ): OrderBuyer
    {
        $latestVersion = $this->buyers()->max('version') ?? 0;

        return $this->buyers()->create([
            'first_name' => $firstName,
            'last_name' => $lastName,
            'email' => $email,
            'phone' => $phone,
            'version' => $latestVersion + 1,
        ]);
    }

    public function updateAddress(
        string $address,
        string $city,
        string $postalCode,
        string $country,
        ?string $state = null
    ): OrderAddress
    {
        $latestVersion = $this->addresses()->max('version') ?? 0;

        return $this->addresses()->create([
            'address' => $address,
            'city' => $city,
            'postal_code' => $postalCode,
            'country' => $country,
            'state' => $state,
            'version' => $latestVersion + 1,
        ]);
    }


    public function updateBilling(
        string $companyName,
        string $taxId,
        ?string $companyAddress = null
    ): OrderBilling
    {
        $latestVersion = $this->billings()->max('version') ?? 0;

        return $this->billings()->create([
            'company_name' => $companyName,
            'tax_id' => $taxId,
            'company_address' => $companyAddress,
            'version' => $latestVersion + 1,
        ]);
    }

    public function updateItem(
        string $itemId,
        string $name,
        string|Money $price,
        int $quantity,
        ?string $sku = null,
        ?array $attributes = null,
        ?int $version = null
    ): void {
        $item = $this->items->first(fn($i) => $i->id === $itemId);
        $item->fill([
            'name' => $name,
            'price' => Money::create($price)->getAmount(),
            'quantity' => $quantity,
            'sku' => $sku,
            'attributes' => $attributes,
        ]);

        $this->optimisticPush($version);
    }

    public function restoreBuyerVersion(int $version): OrderBuyer
    {
        $buyer = $this->buyers()
            ->where('version', $version)
            ->firstOrFail();

        $buyerData = $buyer->toArray();
        unset($buyerData['id']);
        unset($buyerData['created_at']);
        unset($buyerData['updated_at']);

        $latestVersion = $this->buyers()->max('version');
        $buyerData['version'] = $latestVersion + 1;

        return $this->buyers()->create($buyerData);
    }

    public function restoreAddressVersion(int $version): OrderAddress
    {
        $address = $this->addresses()
            ->where('version', $version)
            ->firstOrFail();

        $addressData = $address->toArray();
        unset($addressData['id']);
        unset($addressData['created_at']);
        unset($addressData['updated_at']);

        $latestVersion = $this->addresses()->max('version');
        $addressData['version'] = $latestVersion + 1;

        return $this->addresses()->create($addressData);
    }

    public function restoreBillingVersion(int $version): OrderBilling
    {
        $billing = $this->billings()
            ->where('version', $version)
            ->firstOrFail();

        $billingData = $billing->toArray();
        unset($billingData['id']);
        unset($billingData['created_at']);
        unset($billingData['updated_at']);

        $latestVersion = $this->billings()->max('version');
        $billingData['version'] = $latestVersion + 1;

        return $this->billings()->create($billingData);
    }

    public static function generateOrderNumber(): string
    {
        $prefix = 'ORD';
        $timestamp = now()->format('YmdHis');
        $random = str_pad((string)rand(0, 999), 3, '0', STR_PAD_LEFT);

        return $prefix . $timestamp . $random;
    }
}
