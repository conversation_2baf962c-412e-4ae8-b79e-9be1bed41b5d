<?php

declare(strict_types=1);

namespace Order\Domain;

use Illuminate\Database\Eloquent\Concerns\HasTimestamps;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;
use Shared\Domain\ModelAggregateVersionLocking;
use Shared\Money\Money;
use Shared\Money\MoneyCalculator;

/**
 * 
 *
 * @Entity 
 * @property string $id
 * @property string $order_id
 * @property string $name
 * @property string|null $sku
 * @property string $price
 * @property int $quantity
 * @property string $total
 * @property-read Money $price_money
 * @property-read Money $total_money
 * @property-read string|null $currency
 * @property-read int|null $version
 * @property array|null $attributes
 * @property Carbon|null $deleted_at
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property-read Order $order
 * @property-read \Order\Domain\Order $aggregateRoot
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItem newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItem newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItem onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItem query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItem whereAttributes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItem whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItem whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItem whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItem whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItem whereOrderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItem wherePrice($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItem whereQuantity($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItem whereSku($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItem whereTotal($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItem whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItem withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItem withoutTrashed()
 * @mixin \Eloquent
 */
class OrderItem extends Model
{
    use SoftDeletes, HasUuids, HasTimestamps, ModelAggregateVersionLocking;

    protected $table = 'purchase.order_items';

    protected $fillable = [
        'name',
        'sku',
        'price',
        'quantity',
        'attributes',
    ];

    protected $casts = [
        'price' => 'string',
        'quantity' => 'integer',
        'total' => 'string',
        'attributes' => 'array',
    ];

    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * Implementation of the abstract method from ModelAggregateVersionLocking trait.
     * Returns the BelongsTo relation to the aggregate root.
     */
    public function aggregateRoot(): BelongsTo
    {
        return $this->order();
    }

    public function getPriceMoneyAttribute(): Money
    {
        return new Money((string)$this->price);
    }

    public function getTotalMoneyAttribute(): Money
    {
        return new Money((string)$this->total);
    }

    public function getCurrencyAttribute(): ?string
    {
        return $this->order?->currency;
    }

    public function setPriceMoneyAttribute(Money $money): void
    {
        $this->price = $money->getAmount();

        // Recalculate total when price changes
        if ($this->exists && $this->quantity) {
            $this->total = $this->calculateTotal();
        }
    }

    public function setTotalMoneyAttribute(Money $money): void
    {
        // Total cannot be set directly. It is calculated from price × quantity.
    }

    public function setQuantityAttribute(int $quantity): void
    {
        $this->attributes['quantity'] = $quantity;

        // Recalculate total when quantity changes
        if ($this->price) {
            $this->total = $this->calculateTotal();
        }
    }

    private function calculateTotal(): string
    {
        return MoneyCalculator::multiply($this->price, (string)$this->quantity);
    }

    protected static function booted(): void
    {
        static::creating(function (OrderItem $item) {
            if (empty($item->total)) {
                $item->total = $item->calculateTotal();
            }
        });

        static::updating(function (OrderItem $item) {
            if ($item->isDirty(['price', 'quantity'])) {
                $item->total = $item->calculateTotal();
            }
        });
    }
}
