<?php

declare(strict_types=1);

namespace Order\Application\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Order\Application\Event\OrderUpdatedEvent;
use Order\Application\Jobs\AddOrderItemJob;
use Order\Application\Jobs\UpdateOrderAddressJob;
use Order\Application\Jobs\UpdateOrderBuyerJob;
use Order\Domain\Order;

class UpdateOrderJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @param string $orderId Order ID to update
     * @param string|null $notes Additional notes for the order
     * @param array|null $buyer Buyer information
     * @param array|null $address Address information
     * @param array|null $items Order items
     */
    public function __construct(
        public readonly string $orderId,
        public readonly ?string $notes = null,
        public readonly ?array $buyer = null,
        public readonly ?array $address = null,
        public readonly ?array $items = null,
    ) {
    }

    /**
     * Execute the job.
     */
    public function handle(): Order
    {
        return DB::transaction(function () {
            $order = Order::findOrFail($this->orderId);

            if ($this->notes !== null) {
                $order->notes = $this->notes;
                $order->save();
            }

            // Dispatch event for order update
            event(new OrderUpdatedEvent($order->id));

            // Update buyer information using the dedicated job
            if ($this->buyer) {
                UpdateOrderBuyerJob::dispatchSync(
                    orderId: $order->id,
                    firstName: $this->buyer['first_name'],
                    lastName: $this->buyer['last_name'],
                    email: $this->buyer['email'] ?? null,
                    phone: $this->buyer['phone'] ?? null
                );
            }

            // Update address using the dedicated job
            if ($this->address) {
                UpdateOrderAddressJob::dispatchSync(
                    orderId: $order->id,
                    address: $this->address['address'],
                    city: $this->address['city'],
                    postalCode: $this->address['postal_code'],
                    country: $this->address['country'],
                    state: $this->address['state'] ?? null
                );
            }

            // Add order items using the dedicated job
            if ($this->items) {
                foreach ($this->items as $itemData) {
                    AddOrderItemJob::dispatchSync(
                        orderId: $order->id,
                        name: $itemData['name'],
                        price: (float)$itemData['price'],
                        quantity: (int)$itemData['quantity'],
                        sku: $itemData['sku'] ?? null,
                        attributes: $itemData['attributes'] ?? null,
                        id: $itemData['id'] ?? null
                    );
                }
            }

            return $order;
        });
    }
}
