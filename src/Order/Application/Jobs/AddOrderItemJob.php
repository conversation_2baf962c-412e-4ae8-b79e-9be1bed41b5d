<?php

declare(strict_types=1);

namespace Order\Application\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Order\Application\Event\OrderItemAddedEvent;
use Order\Domain\Order;
use Order\Domain\OrderItem;

class AddOrderItemJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @param string $orderId The order ID
     * @param string $name Item name
     * @param float $price Item price
     * @param int $quantity Item quantity
     * @param string|null $sku Item SKU
     * @param array|null $attributes Additional item attributes
     * @param string|null $id Existing item ID for versioning
     */
    public function __construct(
        public readonly string $orderId,
        public readonly string $name,
        public readonly float $price,
        public readonly int $quantity,
        public readonly ?string $sku = null,
        public readonly ?array $attributes = null,
        public readonly ?string $id = null,
    ) {
    }

    /**
     * Execute the job.
     */
    public function handle(): OrderItem
    {
        return DB::transaction(function () {
            $order = Order::findOrFail($this->orderId);

            $item = $order->addItem(
                name: $this->name,
                price: $this->price,
                quantity: $this->quantity,
                sku: $this->sku,
                attributes: $this->attributes,
                id: $this->id
            );

            // Dispatch event
            event(new OrderItemAddedEvent($order->id, $item->id));

            return $item;
        });
    }
}
