<?php

declare(strict_types=1);

namespace Order\Application\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Order\Application\Event\OrderAddressUpdatedEvent;
use Order\Domain\Order;
use Order\Domain\OrderAddress;

class UpdateOrderAddressJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @param string $orderId The order ID
     * @param string $address Full address (street, building, apartment)
     * @param string $city City
     * @param string $postalCode Postal code
     * @param string $country Country
     * @param string|null $state State/province/region
     */
    public function __construct(
        public readonly string $orderId,
        public readonly string $address,
        public readonly string $city,
        public readonly string $postalCode,
        public readonly string $country,
        public readonly ?string $state = null,
    ) {
    }

    /**
     * Execute the job.
     */
    public function handle(): OrderAddress
    {
        return DB::transaction(function () {
            $order = Order::findOrFail($this->orderId);

            $address = $order->updateAddress(
                address: $this->address,
                city: $this->city,
                postalCode: $this->postalCode,
                country: $this->country,
                state: $this->state
            );

            // Dispatch event
            event(new OrderAddressUpdatedEvent($order->id, $address->id));

            return $address;
        });
    }
}
