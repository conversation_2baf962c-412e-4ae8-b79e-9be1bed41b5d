<?php

declare(strict_types=1);

namespace Order\Application\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Order\Application\Event\OrderBuyerUpdatedEvent;
use Order\Domain\Order;
use Order\Domain\OrderBuyer;

class UpdateOrderBuyerJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @param string $orderId The order ID
     * @param string $firstName Buyer's first name
     * @param string $lastName Buyer's last name
     * @param string|null $email Buyer's email
     * @param string|null $phone Buyer's phone number
     */
    public function __construct(
        public readonly string $orderId,
        public readonly string $firstName,
        public readonly string $lastName,
        public readonly ?string $email = null,
        public readonly ?string $phone = null,
    ) {
    }

    /**
     * Execute the job.
     */
    public function handle(): OrderBuyer
    {
        return DB::transaction(function () {
            $order = Order::findOrFail($this->orderId);

            $buyer = $order->updateBuyer(
                firstName: $this->firstName,
                lastName: $this->lastName,
                email: $this->email,
                phone: $this->phone
            );

            // Dispatch event
            event(new OrderBuyerUpdatedEvent($order->id, $buyer->id));

            return $buyer;
        });
    }
}
