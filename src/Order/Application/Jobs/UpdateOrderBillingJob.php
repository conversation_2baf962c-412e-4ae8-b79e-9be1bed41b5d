<?php

declare(strict_types=1);

namespace Order\Application\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Order\Application\Event\OrderBillingUpdatedEvent;
use Order\Domain\Order;
use Order\Domain\OrderBilling;

class UpdateOrderBillingJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @param string $orderId The order ID
     * @param string $companyName Company name
     * @param string $taxId Tax ID / VAT number
     * @param string|null $companyAddress Company address
     */
    public function __construct(
        public readonly string $orderId,
        public readonly string $companyName,
        public readonly string $taxId,
        public readonly ?string $companyAddress = null,
    ) {
    }

    /**
     * Execute the job.
     */
    public function handle(): OrderBilling
    {
        return DB::transaction(function () {
            $order = Order::findOrFail($this->orderId);

            $billing = $order->updateBilling(
                companyName: $this->companyName,
                taxId: $this->taxId,
                companyAddress: $this->companyAddress
            );

            // Dispatch event
            event(new OrderBillingUpdatedEvent($order->id, $billing->id));

            return $billing;
        });
    }
}
