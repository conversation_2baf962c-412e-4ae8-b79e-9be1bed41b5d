<?php

declare(strict_types=1);

namespace Order\Application\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Order\Application\Event\OrderCreatedEvent;
use Order\Domain\Order;

class CreateOrderJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @param string $orderId Pre-generated order ID
     * @param string|null $notes Additional notes for the order
     */
    public function __construct(
        public readonly string $orderId,
        public readonly ?string $notes = null,
    ) {
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        DB::transaction(function () {
            // Create the order with the pre-generated ID
            $order = new Order();
            $order->id = $this->orderId;
            $order->notes = $this->notes;
            $order->save();

            // Dispatch event for order creation
            event(new OrderCreatedEvent($order->id));
        });
    }
}
