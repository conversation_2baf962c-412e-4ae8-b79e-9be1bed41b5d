<?php

namespace Order\UI\Filament\OrderResource\Pages;

use Order\Application\Jobs\UpdateOrderJob;
use Order\UI\Filament\OrderResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Database\Eloquent\Model;

class EditOrder extends EditRecord
{
    protected static string $resource = OrderResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    protected function handleRecordUpdate(Model $record, array $data): Model
    {
        return UpdateOrderJob::dispatchSync(
            orderId: $record->id,
            notes: $data['notes'] ?? null,
        );
    }
}
