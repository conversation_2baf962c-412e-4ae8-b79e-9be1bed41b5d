<?php

namespace Order\UI\Filament\OrderResource\Pages;

use Illuminate\Support\Str;
use Order\Application\Jobs\CreateOrderJob;
use Order\Domain\Order;
use Order\UI\Filament\OrderResource;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Database\Eloquent\Model;

class CreateOrder extends CreateRecord
{
    protected static string $resource = OrderResource::class;

    protected static bool $canCreateAnother = false;

    protected function handleRecordCreation(array $data): Model
    {
        $orderId = (string) Str::uuid();

        CreateOrderJob::dispatch(
            orderId: $orderId,
            notes: $data['notes'] ?? null,
        );

        return Order::findOrFail($orderId);
    }
}
