<?php

declare(strict_types=1);

namespace Order\UI\Filament\OrderResource\RelationManagers;

use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;


class OrderBuyersRelationManager extends RelationManager
{
    protected static string $relationship = 'buyers';

    protected static ?string $recordTitleAttribute = 'full_name';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Buyer Details')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextInput::make('first_name')
                                    ->required()
                                    ->maxLength(255)
                                    ->columnSpan(1),
                                TextInput::make('last_name')
                                    ->required()
                                    ->maxLength(255)
                                    ->columnSpan(1),
                            ]),
                        Grid::make(2)
                            ->schema([
                                TextInput::make('email')
                                    ->email()
                                    ->maxLength(255)
                                    ->columnSpan(1),
                                TextInput::make('phone')
                                    ->tel()
                                    ->maxLength(255)
                                    ->columnSpan(1),
                            ]),
                        Grid::make(2)
                            ->schema([
                                TextInput::make('company_name')
                                    ->maxLength(255)
                                    ->columnSpan(1),
                                TextInput::make('tax_id')
                                    ->maxLength(255)
                                    ->columnSpan(1),
                            ]),

                        Hidden::make('version')
                            ->default(1),
                    ]),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('full_name')
            ->columns([
                TextColumn::make('first_name')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('last_name')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('email')
                    ->searchable(),
                TextColumn::make('phone')
                    ->searchable(),
                TextColumn::make('company_name')
                    ->searchable(),
                TextColumn::make('version')
                    ->sortable(),
                TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->createAnother(false)
                    ->using(function (array $data, RelationManager $livewire): mixed {
                        return $livewire->getOwnerRecord()->updateBuyer($data);
                    }),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->using(function (array $data, RelationManager $livewire): mixed {
                        return $livewire->getOwnerRecord()->updateBuyer($data);
                    }),
                Tables\Actions\Action::make('restore_version')
                    ->label(__d('order', 'Przywróć wersję'))
                    ->icon('heroicon-o-arrow-path')
                    ->color('success')
                    ->action(function (mixed $record, RelationManager $livewire): mixed {
                        return $livewire->getOwnerRecord()->restoreBuyerVersion($record->version);
                    })
                    ->requiresConfirmation()
                    ->modalHeading(__d('order', 'Przywróć wersję'))
                    ->modalDescription(__d('order', 'Czy na pewno chcesz przywrócić tę wersję? Zostanie utworzona nowa wersja na podstawie tej.')),
                Tables\Actions\DeleteAction::make(),
                Tables\Actions\ForceDeleteAction::make(),
                Tables\Actions\RestoreAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ])
            ->modifyQueryUsing(function (Builder $query) {
                // Show all versions, sorted by version descending
                return $query->orderByDesc('version');
            });
    }
}
