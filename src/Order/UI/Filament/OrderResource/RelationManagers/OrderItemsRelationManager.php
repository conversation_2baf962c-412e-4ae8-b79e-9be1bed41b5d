<?php

declare(strict_types=1);

namespace Order\UI\Filament\OrderResource\RelationManagers;

use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Order\Domain\OrderItem;
use Shared\Money\MoneyCalculator;

class OrderItemsRelationManager extends RelationManager
{
    protected static string $relationship = 'items';

    protected static ?string $recordTitleAttribute = 'name';

    protected function getOrderItemFormSchema(): array
    {
        return [
            Section::make()
                ->schema([
                    Grid::make(2)
                        ->schema([
                            TextInput::make('name')
                                ->label(__d('order', 'Nazwa'))
                                ->required()
                                ->maxLength(255)
                                ->columnSpan(1, 2),
                            TextInput::make('sku')
                                ->label(__d('order', 'SKU'))
                                ->maxLength(255)
                                ->columnSpan(1),
                        ]),
                ]),
            Section::make(__d('order', 'Cena'))
                ->schema([
                    Grid::make(3)
                        ->schema([
                            TextInput::make('price')
                                ->label(__d('order', 'Cena'))
                                ->required()
                                ->numeric()
                                ->minValue(0)
                                ->live()
                                ->suffix(fn(?OrderItem $record = null) => $record?->currency ?? '')
                                ->afterStateUpdated(function (Get $get, Set $set, ?string $state) {
                                    $total = MoneyCalculator::multiply(
                                        (string)$state,
                                        (string)$get('quantity')
                                    );
                                    $set('total', $total);
                                })
                                ->columnSpan(1),
                            TextInput::make('quantity')
                                ->label(__d('order', 'Ilość'))
                                ->required()
                                ->numeric()
                                ->minValue(1)
                                ->default(1)
                                ->live()
                                ->afterStateUpdated(function (Get $get, Set $set, ?string $state) {
                                    $total = MoneyCalculator::multiply(
                                        (string)$get('price'),
                                        (string)$state,
                                        2
                                    );
                                    $set('total', $total);
                                })
                                ->columnSpan(1),
                            TextInput::make('total')
                                ->label(__d('order', 'Suma'))
                                ->numeric()
                                ->disabled()
                                ->suffix(fn(?OrderItem $record = null) => $record?->currency ?? '')
                                ->hint(__d('order', 'obliczana automatycznie'))
                                ->helperText(__d('order', 'cena × Ilość'))
                                ->columnSpan(1),
                        ]),
                ]),
            Hidden::make('version')
                ->default(1),
        ];
    }

    public function form(Form $form): Form
    {
        return $form->schema($this->getOrderItemFormSchema());
    }

    public function table(Table $table): Table
    {
        return $table
            ->heading(__d('order', 'Pozycje zamówienia'))
            ->recordTitleAttribute('name')
            ->columns([
                TextColumn::make('name')
                    ->label(__d('order', 'Nazwa'))
                    ->searchable()
                    ->sortable()
                    ->toggleable(),
                TextColumn::make('sku')
                    ->label(__d('order', 'SKU'))
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('price')
                    ->money(fn(OrderItem $record) => $record->order->currency)
                    ->label(__d('order', 'Cena'))
                    ->sortable()
                    ->toggleable()
                    ->summarize(
                        Tables\Columns\Summarizers\Summarizer::make()
                            ->label(__d('order', 'Średnia'))
                            ->using(function (QueryBuilder $query): string {
                                $average = $query->average('price');
                                return MoneyCalculator::normalizeAmount((string)$average);
                            })
                    ),
                TextColumn::make('quantity')
                    ->label(__d('order', 'Ilość'))
                    ->sortable()
                    ->toggleable()
                    ->summarize([
                        Tables\Columns\Summarizers\Summarizer::make()
                            ->label(__d('order', 'Suma'))
                            ->using(function (QueryBuilder $query): string {
                                $sum = $query->sum('quantity');
                                return MoneyCalculator::normalizeAmount((string)$sum, 0);
                            }),
                        Tables\Columns\Summarizers\Summarizer::make()
                            ->label(__d('order', 'Średnia'))
                            ->using(function (QueryBuilder $query): string {
                                $average = $query->average('quantity');
                                return MoneyCalculator::normalizeAmount((string)$average);
                            })
                    ]),
                TextColumn::make('total')
                    ->money(fn(OrderItem $record) => $record->order->currency)
                    ->label(__d('order', 'Suma'))
                    ->sortable()
                    ->toggleable()
                    ->summarize(
                        Tables\Columns\Summarizers\Summarizer::make()
                            ->label(__d('order', 'Razem'))
                            ->using(function (QueryBuilder $query): string {
                                $sum = $query->sum('total');
                                return MoneyCalculator::normalizeAmount((string)$sum);
                            })
                    ),
                TextColumn::make('created_at')
                    ->label(__d('order', 'Data utworzenia'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('updated_at')
                    ->label(__d('order', 'Data aktualizacji'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->createAnother(false)
                    ->label(__d('order', 'Dodaj pozycję'))
                    ->using(function (array $data, RelationManager $livewire): mixed {
                        return $livewire->getOwnerRecord()->addItem(
                            name: $data['name'],
                            price: $data['price'],
                            quantity: (int)$data['quantity'],
                            sku: $data['sku'] ?? null,
                            attributes: $data['attributes'] ?? null,
                            id: $data['id'] ?? null
                        );
                    }),
                Tables\Actions\CreateAction::make('add_multiple_items')
                    ->createAnother(false)
                    ->label(__d('order', 'Dodaj wiele'))
                    ->icon('heroicon-o-plus-circle')
                    ->form([
                        \Filament\Forms\Components\Repeater::make('items')
                            ->schema($this->getOrderItemFormSchema())
                            ->itemLabel(fn(array $state): ?string => $state['name'] ?? __d('order', 'Nowa pozycja'))
                            ->addActionLabel(__d('order', 'Dodaj pozycję'))
                            ->reorderableWithButtons()
                            ->collapsible()
                            ->cloneable()
                            ->defaultItems(1)
                            ->minItems(1)
                            ->columns(1)
                    ])
                    ->action(function (array $data, RelationManager $livewire): void {
                        $order = $livewire->getOwnerRecord();
                        foreach ((array)$data['items'] as $item) {
                            $order->addItem(
                                name: $item['name'],
                                price: $item['price'],
                                quantity: (int)$item['quantity'],
                                sku: $item['sku'] ?? null,
                                attributes: $item['attributes'] ?? null
                            );
                        }
                    }),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->using(function (OrderItem $record, array $data, RelationManager $livewire) {
                        return $livewire->getOwnerRecord()->updateItem(
                            itemId: $record->id,
                            name: $data['name'],
                            price: $data['price'],
                            quantity: (int)$data['quantity'],
                            sku: $data['sku'] ?? null,
                            attributes: $data['attributes'] ?? null,
                            version: isset($data['version']) ? (int)$data['version'] : $record->version
                        );
                    }),
                Tables\Actions\EditAction::make('duplicate_item')
                    ->label(__d('order', 'Duplikuj'))
                    ->icon('heroicon-o-document-duplicate')
                    ->color('success')
                    ->action(function (OrderItem $record, array $data, RelationManager $livewire) {
                        return $livewire->getOwnerRecord()->addItem(
                            name: $data['name'],
                            price: $data['price'],
                            quantity: (int)$data['quantity'],
                            sku: $data['sku'] ?? null,
                            attributes: $record->attributes
                        );
                    })
                    ->modalHeading(__d('order', 'Duplikuj pozycję')),
                Tables\Actions\DeleteAction::make(),
                Tables\Actions\RestoreAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ])
            ->toggleColumnsTriggerAction(function (Action $action): Action {
                return $action
                    ->label(__d('order', 'Kolumny'));
            });
    }
}
