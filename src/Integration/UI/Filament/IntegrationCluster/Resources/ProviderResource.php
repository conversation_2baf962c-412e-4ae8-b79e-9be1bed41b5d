<?php

declare(strict_types=1);

namespace Integration\UI\Filament\IntegrationCluster\Resources;

use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Support\Enums\MaxWidth;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Table;
use Integration\Domain\Provider;
use Integration\UI\Filament\IntegrationCluster;
use Integration\UI\Filament\IntegrationCluster\Resources\ProviderResource\Pages\CreateProvider;
use Integration\UI\Filament\IntegrationCluster\Resources\ProviderResource\Pages\EditProvider;
use Integration\UI\Filament\IntegrationCluster\Resources\ProviderResource\Pages\ListProviders;
use Integration\UI\Filament\IntegrationCluster\Resources\ProviderResource\RelationManagers\IntegrationsRelationManager;

class ProviderResource extends Resource
{
    protected static ?string $model = Provider::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $cluster = IntegrationCluster::class;

    public static function getModelLabel(): string
    {
        return __d('integration', 'Dostępne integracje');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make([
                    TextInput::make('name')->required(),
                    TextInput::make('manifest')->required(),
                ])->description(__d('integration', 'Podstawowe dane')),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name'),
                Tables\Columns\TextColumn::make('integrations_count')
                    ->label('installed')
                    ->badge()
                    ->counts('integrations')
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                self::installAction()
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function installAction(): Action
    {
        return Action::make('install')
            ->label('Install')
            ->form(fn(Provider $record) => [
                TextInput::make('name')
                    ->required()
                    ->default($record->name)
                    ->placeholder(__d('integration', 'Nazwa integracji :provider', ['provider' => $record->name])),
            ])
            ->action(function (array $data, Provider $record) {
                $integration = $record->integrations()->create($data);

                return redirect(IntegrationResource::getUrl('edit', [$integration]));
            })
            ->modalWidth(MaxWidth::Large)
            ->modalHeading(__d('integration', 'Zainstaluj integracje'));
    }

    public static function getRelations(): array
    {
        return [
            IntegrationsRelationManager::class
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListProviders::route('/'),
            'create' => CreateProvider::route('/create'),
            'edit' => EditProvider::route('/{record}/edit'),
        ];
    }

    public static function canViewAny(): bool
    {
        return true;
    }
}
