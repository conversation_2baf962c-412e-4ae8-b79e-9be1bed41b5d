<?php

declare(strict_types=1);

namespace Integration\UI\Filament\IntegrationCluster\Resources\IntegrationResource\Widgets;

use Filament\Support\Colors\Color;
use Filament\Widgets\StatsOverviewWidget;
use Integration\Domain\Integration;

class IntegrationAuthorization extends StatsOverviewWidget
{
    public ?Integration $record = null;

    protected function getStats(): array
    {
        if (null === $this->record) {
            return [];
        }

        return [
            StatsOverviewWidget\Stat::make(__d('integration', 'Status'), $this->record->isAuthorized() ? __d('integration', 'Połączono') : __d('integration', 'Nie połączono'))
                ->color($this->record->isAuthorized() ? Color::Green : Color::Red)
                ->description($this->record->isAuthorized() ? __d('integration', 'Ostatnia udana autoryzacja :lastLogin', ['lastLogin' => $this->record->diffOfLastAuthorization()]) : __d('integration', 'Wymagana autoryzacja'))
                ->icon($this->record->isAuthorized() ? 'heroicon-m-check-circle' : 'heroicon-m-x-circle'),
        ];
    }
}
