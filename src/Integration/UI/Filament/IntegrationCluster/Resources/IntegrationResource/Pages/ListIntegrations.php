<?php

declare(strict_types=1);

namespace Integration\UI\Filament\IntegrationCluster\Resources\IntegrationResource\Pages;

use Filament\Pages\SubNavigationPosition;
use Filament\Resources\Pages\ListRecords;
use Integration\UI\Filament\IntegrationCluster;
use Integration\UI\Filament\IntegrationCluster\Resources\IntegrationResource;

class ListIntegrations extends ListRecords
{
    protected static string $resource = IntegrationResource::class;

    protected static ?string $cluster = IntegrationCluster::class;

    public function getSubNavigationPosition(): SubNavigationPosition
    {
        return SubNavigationPosition::Start;
    }

    protected function getHeaderActions(): array
    {
        return [
        ];
    }
}
