<?php

declare(strict_types=1);

namespace Integration\UI\Filament\IntegrationCluster\Resources\IntegrationResource\Pages;

use Filament\Actions\Action;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;
use Integration\Application\IntegrationService;
use Integration\Domain\Integration;
use Integration\UI\Filament\IntegrationCluster;
use Integration\UI\Filament\IntegrationCluster\Resources\IntegrationResource;
use Integration\UI\Filament\IntegrationCluster\Resources\IntegrationResource\Widgets\IntegrationAuthorization;

class EditIntegration extends EditRecord
{
    protected static string $resource = IntegrationResource::class;

    protected static ?string $cluster = IntegrationCluster::class;

    private IntegrationService $integrationService;

    public function __construct()
    {
        $this->integrationService = app(IntegrationService::class);
    }

    public function beforeFill(): void
    {
        if (request()->has('code')) {
            $this->integrationService->exchangeAuthorizationCode(
                $this->getRecord(),
                request('code'),
                request()->url()
            );

            Notification::make()
                ->title('Autoryzacja przeprowadzona pomyślnie')
                ->success()
                ->send();

            $redirectUrl = IntegrationResource\Pages\EditIntegration::getUrl([$this->getRecord()]);
            $this->redirect($redirectUrl);
        }
    }

    protected function getHeaderActions(): array
    {
        $action = function (Integration $integration) {
            // try refresh token
            if ($integration->isAuthorized() && $this->integrationService->refreshToken($integration)) {
                Notification::make()
                    ->title('Autoryzacja odświeżona pomyślnie')
                    ->success()
                    ->send();
                return;
            }

            // proceed to authorization
            $redirectUrl = IntegrationResource\Pages\EditIntegration::getUrl([$integration]);
            $authorizeUrl = $this->integrationService->generateAuthorizationUrl($integration, $redirectUrl);

            redirect($authorizeUrl);
        };

        return [
            Action::make('authorize')
                ->label(__d('integrations', 'Zaloguj się'))
                ->requiresConfirmation()
                ->action($action)
        ];
    }

    protected function getHeaderWidgets(): array
    {
        return [
            IntegrationAuthorization::class
        ];
    }
}
