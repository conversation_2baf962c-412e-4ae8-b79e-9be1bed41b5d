<?php

declare(strict_types=1);

namespace Integration\UI\Filament\IntegrationCluster\Resources\ProviderResource\Pages;

use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Integration\UI\Filament\IntegrationCluster\Resources\ProviderResource;

class ListProviders extends ListRecords
{
    protected static string $resource = ProviderResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
