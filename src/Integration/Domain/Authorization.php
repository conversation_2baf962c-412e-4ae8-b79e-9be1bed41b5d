<?php

declare(strict_types=1);

namespace Integration\Domain;

use Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;

/**
 * 
 *
 * @property string $id
 * @property string $integration_id
 * @property Carbon|null $deleted_at
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property-read Provider $integration
 * @method static Builder<static>|Authorization newModelQuery()
 * @method static Builder<static>|Authorization newQuery()
 * @method static Builder<static>|Authorization onlyTrashed()
 * @method static Builder<static>|Authorization query()
 * @method static Builder<static>|Authorization whereCreatedAt($value)
 * @method static Builder<static>|Authorization whereDeletedAt($value)
 * @method static Builder<static>|Authorization whereId($value)
 * @method static Builder<static>|Authorization whereIntegrationId($value)
 * @method static Builder<static>|Authorization whereToken($value)
 * @method static Builder<static>|Authorization whereUpdatedAt($value)
 * @method static Builder<static>|Authorization withTrashed()
 * @method static Builder<static>|Authorization withoutTrashed()
 * @property array<array-key, mixed> $authorization
 * @method static Builder<static>|Authorization whereAuthorization($value)
 * @method static Builder<static>|Authorization last()
 * @mixin Eloquent
 */
class Authorization extends Model
{
    use SoftDeletes, HasUuids;

    protected $table = 'integration.authorizations';

    protected $fillable = [
        'authorization',
    ];

    protected $casts = [
        'authorization' => 'array'
    ];

    public function integration(): BelongsTo
    {
        return $this->belongsTo(Integration::class);
    }
}
