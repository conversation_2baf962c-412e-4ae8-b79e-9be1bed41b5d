<?php

declare(strict_types=1);

namespace Integration\Domain;

use Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;

/**
 * 
 *
 * @property string $id
 * @property string $name
 * @property string $manifest
 * @property Carbon|null $deleted_at
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property-read Collection<int, Integration> $integrations
 * @property-read int|null $integrations_count
 * @method static Builder<static>|Provider newModelQuery()
 * @method static Builder<static>|Provider newQuery()
 * @method static Builder<static>|Provider onlyTrashed()
 * @method static Builder<static>|Provider query()
 * @method static Builder<static>|Provider whereCreatedAt($value)
 * @method static Builder<static>|Provider whereDeletedAt($value)
 * @method static Builder<static>|Provider whereId($value)
 * @method static Builder<static>|Provider whereManifest($value)
 * @method static Builder<static>|Provider whereName($value)
 * @method static Builder<static>|Provider whereUpdatedAt($value)
 * @method static Builder<static>|Provider withTrashed()
 * @method static Builder<static>|Provider withoutTrashed()
 * @method static \Database\Factories\Integration\Domain\ProviderFactory factory($count = null, $state = [])
 * @mixin Eloquent
 */
class Provider extends Model
{
    use SoftDeletes, HasUuids, HasFactory;

    protected $table = 'integration.providers';

    protected $fillable = [
        'name',
        'manifest'
    ];

    public function integrations(): HasMany
    {
        return $this->hasMany(Integration::class, 'provider_id');
    }
}
