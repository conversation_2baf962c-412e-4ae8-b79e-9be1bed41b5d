<?php

declare(strict_types=1);

namespace Integration\Domain;

use Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;

/**
 * 
 *
 * @property string $id
 * @property string $name
 * @property string $provider_id
 * @property Carbon|null $deleted_at
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property-read Provider $provider
 * @method static Builder<static>|Integration newModelQuery()
 * @method static Builder<static>|Integration newQuery()
 * @method static Builder<static>|Integration onlyTrashed()
 * @method static Builder<static>|Integration query()
 * @method static Builder<static>|Integration whereCreatedAt($value)
 * @method static Builder<static>|Integration whereDeletedAt($value)
 * @method static Builder<static>|Integration whereId($value)
 * @method static Builder<static>|Integration whereName($value)
 * @method static Builder<static>|Integration whereProviderId($value)
 * @method static Builder<static>|Integration whereUpdatedAt($value)
 * @method static Builder<static>|Integration withTrashed()
 * @method static Builder<static>|Integration withoutTrashed()
 * @property-read string $manifest
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Integration\Domain\Authorization> $authorizations
 * @property-read int|null $authorizations_count
 * @method static \Database\Factories\Integration\Domain\IntegrationFactory factory($count = null, $state = [])
 * @mixin Eloquent
 */
class Integration extends Model implements IntegrationManifestInterface
{
    use SoftDeletes, HasUuids, HasFactory;

    protected $table = 'integration.integrations';

    protected $fillable = [
        'name'
    ];

    public function provider(): BelongsTo
    {
        return $this->belongsTo(Provider::class);
    }

    public function authorizations(): HasMany
    {
        return $this->hasMany(Authorization::class);
    }

    public function getManifest(): string
    {
        return $this->provider->manifest;
    }

    public function authorize(array $authorizationData): void
    {
        $this->getConnection()->transaction(function () use ($authorizationData) {
            $this->clearAuthorizations();
            $this->authorizations()->create([
                'authorization' => $authorizationData
            ]);
        });
    }

    public function clearAuthorizations(): void
    {
        $this->authorizations()->forceDelete();
    }

    public function isAuthorized(): bool
    {
        return $this->authorizations()->exists();
    }

    public function diffOfLastAuthorization(): string
    {
        return $this->authorizations
            ->last(fn(Authorization $authorization) => $authorization->created_at)
            ?->created_at->diffForHumans() ?: '--';
    }

    public function latestAuthorizations(): Authorization
    {
        return $this->authorizations()->latest()->first();
    }
}
