<?php

declare(strict_types=1);

namespace Integration\Domain;

use Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;

/**
 * 
 *
 * @property string $id
 * @property array<array-key, mixed> $configuration
 * @property string $integration_id
 * @property Carbon|null $deleted_at
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property-read Provider|null $provider
 * @method static Builder<static>|Configuration newModelQuery()
 * @method static Builder<static>|Configuration newQuery()
 * @method static Builder<static>|Configuration onlyTrashed()
 * @method static Builder<static>|Configuration query()
 * @method static Builder<static>|Configuration whereConfiguration($value)
 * @method static Builder<static>|Configuration whereCreatedAt($value)
 * @method static Builder<static>|Configuration whereDeletedAt($value)
 * @method static Builder<static>|Configuration whereId($value)
 * @method static Builder<static>|Configuration whereIntegrationId($value)
 * @method static Builder<static>|Configuration whereUpdatedAt($value)
 * @method static Builder<static>|Configuration withTrashed()
 * @method static Builder<static>|Configuration withoutTrashed()
 * @mixin Eloquent
 */
class Configuration extends Model
{
    use SoftDeletes, HasUuids;

    protected $table = 'integration.configurations';

    protected $fillable = [
        'configuration'
    ];

    protected $casts = [
        'configuration' => 'array'
    ];

    public function provider(): BelongsTo
    {
        return $this->belongsTo(Provider::class);
    }
}
