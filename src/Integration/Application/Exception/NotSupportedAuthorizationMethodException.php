<?php

declare(strict_types=1);

namespace Integration\Application\Exception;

use Integration\Domain\Integration;
use Shared\Error\ContextExceptionInterface;
use Shared\Error\ContextExceptionTrait;

class NotSupportedAuthorizationMethodException extends \RuntimeException implements ContextExceptionInterface
{
    use ContextExceptionTrait;

    private function __construct(string $message = "", int $code = 0, ?\Throwable $previous = null)
    {
        parent::__construct($message, $code, $previous);
    }

    public static function forIntegration(
        Integration $integration,
        string $message = "",
        int $code = 0,
        ?\Throwable $previous = null
    ): self {
        $exception = new self($message, $code, $previous);
        $exception->addContext('integration_id', $integration->id);
        return $exception;
    }
}
