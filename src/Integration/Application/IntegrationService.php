<?php

declare(strict_types=1);

namespace Integration\Application;

use Integration\Application\Exception\NotSupportedAuthorizationMethodException;
use Integration\Domain\Integration;
use Integration\Domain\IntegrationManifestInterface;
use Psr\Container\ContainerInterface;
use Shared\Integration\Abstract\Port\Authorization\Method\OAuthCodeFlowAbstractAuthorizationInterface;
use Shared\Integration\Abstract\Port\Exception\AllegroAuthorizationException;
use Shared\Integration\Abstract\Port\IntegrationInterface;

final class IntegrationService
{
    public function __construct(private ContainerInterface $container)
    {
    }

    public function generateAuthorizationUrl(IntegrationManifestInterface $integration, string $redirectUrl): string
    {
        $manifest = $this->manifest($integration);
        $authorization = $manifest->authorizator();

        if ($authorization instanceof OAuthCodeFlowAbstractAuthorizationInterface) {
            return $authorization->authorizationUrl($redirectUrl);
        }

        throw NotSupportedAuthorizationMethodException::forIntegration($integration);
    }

    public function exchangeAuthorizationCode(
        Integration $integration,
        string $code,
        string $redirectUrl
    ): void {
        $manifest = $this->manifest($integration);
        $authorization = $manifest->authorizator();

        if ($authorization instanceof OAuthCodeFlowAbstractAuthorizationInterface) {
            $authorizationResult = $authorization->exchangeCode($code, $redirectUrl);

            $integration->authorize($authorizationResult->toArray());

            return;
        }

        throw NotSupportedAuthorizationMethodException::forIntegration($integration);
    }

    public function refreshToken(Integration $integration): bool
    {
        $manifest = $this->manifest($integration);
        $authorizator = $manifest->authorizator();

        if ($authorizator instanceof OAuthCodeFlowAbstractAuthorizationInterface) {
            $authorization = $integration->latestAuthorizations();

            try {
                $authorizationResult = $authorizator->refreshToken($authorization->authorization);
                $integration->authorize($authorizationResult->toArray());
                return true;
            } catch (AllegroAuthorizationException $e) {
                report($e);
                return false;
            }
        }

        throw NotSupportedAuthorizationMethodException::forIntegration($integration);
    }

    private function manifest(IntegrationManifestInterface $integration): IntegrationInterface
    {
        return $this->container->get($integration->getManifest());
    }
}
