<?php

declare(strict_types=1);

namespace Offer\Domain;

use Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;

/**
 * 
 *
 * @property string $id
 * @property string $title
 * @property string|null $content
 * @property Carbon|null $deleted_at
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @method static Builder<static>|Offer newModelQuery()
 * @method static Builder<static>|Offer newQuery()
 * @method static Builder<static>|Offer onlyTrashed()
 * @method static Builder<static>|Offer query()
 * @method static Builder<static>|Offer whereContent($value)
 * @method static Builder<static>|Offer whereCreatedAt($value)
 * @method static Builder<static>|Offer whereDeletedAt($value)
 * @method static Builder<static>|Offer whereId($value)
 * @method static Builder<static>|Offer whereTitle($value)
 * @method static Builder<static>|Offer whereUpdatedAt($value)
 * @method static Builder<static>|Offer withTrashed()
 * @method static Builder<static>|Offer withoutTrashed()
 * @mixin Eloquent
 */
class Offer extends Model
{
    use SoftDeletes, HasUuids;

    protected $table = 'offer.offers';

    protected $fillable = [
        'title',
        'content',
    ];
}
