<?php

declare(strict_types=1);

namespace Offer\UI\Filament\OfferResource\Pages;

use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Offer\UI\Filament\OfferResource;

class ListOffers extends ListRecords
{
    protected static string $resource = OfferResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
