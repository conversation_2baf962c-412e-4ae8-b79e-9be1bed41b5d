<?php

declare(strict_types=1);

namespace Offer\UI\Filament\OfferResource\Pages;

use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Offer\UI\Filament\OfferResource;

class EditOffer extends EditRecord
{
    protected static string $resource = OfferResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
            Actions\ForceDeleteAction::make(),
            Actions\RestoreAction::make(),
        ];
    }
}
