<?php

declare(strict_types=1);

namespace Offer\UI\Filament\OfferResource;

use Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;
use Offer\Domain\Offer;
use Product\Domain\Product;

/**
 * 
 *
 * @property string $id
 * @property string $title
 * @property string|null $content
 * @property Carbon|null $deleted_at
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property-read Product|null $product
 * @method static Builder<static>|OfferModel newModelQuery()
 * @method static Builder<static>|OfferModel newQuery()
 * @method static Builder<static>|OfferModel onlyTrashed()
 * @method static Builder<static>|OfferModel query()
 * @method static Builder<static>|OfferModel whereContent($value)
 * @method static Builder<static>|OfferModel whereCreatedAt($value)
 * @method static Builder<static>|OfferModel whereDeletedAt($value)
 * @method static Builder<static>|OfferModel whereId($value)
 * @method static Builder<static>|OfferModel whereTitle($value)
 * @method static Builder<static>|OfferModel whereUpdatedAt($value)
 * @method static Builder<static>|OfferModel withTrashed()
 * @method static Builder<static>|OfferModel withoutTrashed()
 * @mixin Eloquent
 */
class OfferModel extends Offer
{
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }
}
